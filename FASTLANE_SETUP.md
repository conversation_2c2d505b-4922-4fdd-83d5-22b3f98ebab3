# Fastlane Setup Guide for TravelGator iOS

This guide will help you set up and use Fastlane for automated iOS builds and deployments for the TravelGator Flutter app.

## 🚀 Quick Start

### 1. Prerequisites

- Xcode installed and configured
- Apple Developer account
- Flutter SDK installed
- Ruby (already available on macOS)

### 2. Initial Setup

```bash
# Navigate to iOS directory
cd ios

# Install dependencies
./fastlane_helper.sh setup

# Or manually:
bundle install --path vendor/bundle
```

### 3. Environment Configuration

```bash
# Copy environment template
cp fastlane/.env.default fastlane/.env

# Edit with your values
nano fastlane/.env
```

**Required Environment Variables:**
- `APPLE_ID`: Your Apple Developer account email
- `ITC_TEAM_ID`: iTunes Connect team ID (if different from developer team)
- `MATCH_GIT_URL`: Git repository for storing certificates
- `MATCH_PASSWORD`: Password for encrypting certificates

### 4. Certificate Setup (First Time Only)

```bash
# Initialize match for certificate management
bundle exec fastlane match init

# Generate and store certificates
./fastlane_helper.sh sync
```

## 📱 Available Commands

### Using the Helper Script

```bash
# Build the app
./fastlane_helper.sh build

# Run tests
./fastlane_helper.sh test

# Deploy to TestFlight
./fastlane_helper.sh beta

# Deploy to App Store
./fastlane_helper.sh release

# Manage certificates
./fastlane_helper.sh certs

# Show all available lanes
./fastlane_helper.sh lanes
```

### Using Fastlane Directly

```bash
# Build
bundle exec fastlane build

# Test
bundle exec fastlane test

# Beta deployment
bundle exec fastlane beta

# Release deployment
bundle exec fastlane release

# Certificate management
bundle exec fastlane certificates
bundle exec fastlane sync_certificates

# Screenshots
bundle exec fastlane screenshots
```

## 🔧 Configuration Files

### `ios/fastlane/Appfile`
Contains app-specific configuration:
- Bundle identifier: `com.travelgator.development`
- Team ID: `5K25T9R4G7`
- Apple ID from environment variables

### `ios/fastlane/Fastfile`
Contains all automation lanes:
- **build**: Builds the iOS app for release
- **test**: Runs Flutter and iOS unit tests
- **beta**: Deploys to TestFlight with build number increment
- **release**: Deploys to App Store with version increment
- **certificates**: Downloads existing certificates
- **sync_certificates**: Updates/creates certificates
- **screenshots**: Generates App Store screenshots

### `ios/fastlane/Matchfile`
Certificate management configuration:
- Uses Git for storing certificates
- Configured for the TravelGator bundle ID
- Encrypts certificates with password

## 🔐 Security Best Practices

1. **Never commit sensitive files:**
   - `.env` files are already in `.gitignore`
   - App Store Connect API keys are excluded
   - Certificate repositories should be private

2. **Use App Store Connect API Keys:**
   - More secure than username/password
   - Better for CI/CD environments
   - Configure in `.env` file

3. **Certificate Management:**
   - Use match for team certificate sharing
   - Store certificates in private Git repository
   - Use strong encryption password

## 🚀 CI/CD Integration

### GitHub Actions Example

```yaml
name: iOS Build and Deploy

on:
  push:
    branches: [main]

jobs:
  ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
          bundler-cache: true
          working-directory: ios
          
      - name: Deploy to TestFlight
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          MATCH_GIT_URL: ${{ secrets.MATCH_GIT_URL }}
        run: |
          cd ios
          bundle exec fastlane beta
```

## 🐛 Troubleshooting

### Common Issues

1. **Certificate Problems:**
   ```bash
   # Reset and sync certificates
   bundle exec fastlane sync_certificates
   ```

2. **Build Failures:**
   ```bash
   # Clean and rebuild
   flutter clean
   cd ios
   bundle exec fastlane build
   ```

3. **Permission Issues:**
   ```bash
   # Ensure proper team and bundle ID configuration
   # Check Appfile and Xcode project settings
   ```

4. **Upload Failures:**
   - Verify Apple ID and team ID
   - Check App Store Connect access
   - Ensure app exists in App Store Connect

### Getting Help

- Check Fastlane logs in `ios/fastlane/`
- Review Xcode build logs
- Verify environment variables
- Consult [Fastlane Documentation](https://docs.fastlane.tools/)

## 📋 Next Steps

1. Configure your `.env` file with actual values
2. Set up certificate management with match
3. Test the build lane locally
4. Set up CI/CD pipeline
5. Configure Slack notifications (optional)
6. Set up screenshot automation (optional)

For detailed information about each lane, see `ios/fastlane/README.md`.
