# TravelGator User Flows Architecture

## Overview
This document outlines the high-level user flows and system interactions in the TravelGator application. The application follows a clean architecture pattern with clear separation between presentation, domain, and data layers.

## Core User Flows

### 1. First-Time User Experience
```mermaid
sequenceDiagram
    User->>App: Opens app
    App->>Shopify API: Fetch products
    Shopify API-->>App: Return products
    App->>User: Display product listing
```

#### Implementation Details
- **Entry Point**: `lib/features/products/presentation/pages/product_list_screen.dart`
- **Data Source**: Shopify Storefront API
- **State Management**: Product list provider
- **Key Features**:
  - Product listing with images
  - Product search and filtering
  - Product categories
  - Product details view

### 2. Authentication Flow
```mermaid
sequenceDiagram
    User->>App: Clicks "Add to Cart"
    App->>User: Prompt for login
    User->>App: Initiates Google Sign-In
    App->>Firebase: Authenticate with Google
    Firebase-->>App: Return Firebase token
    App->>Backend: Authenticate with Firebase token
    Backend-->>App: Return JWT token
    App->>App: Store tokens securely
    App->>User: Complete login
```

#### Implementation Details
- **Entry Points**:
  - `lib/features/auth/presentation/screens/sign_in_screen.dart`
  - `lib/features/auth/presentation/providers/auth_provider.dart`
- **Authentication Services**:
  - Firebase Authentication
  - Backend JWT Authentication
- **Key Features**:
  - Google Sign-In
  - Token management
  - Session persistence
  - Secure storage

### 3. Cart Management Flow
```mermaid
sequenceDiagram
    User->>App: Adds/updates cart items
    App->>Backend: Cart API requests
    Backend-->>App: Updated cart data
    App->>User: Update cart UI
```

#### Implementation Details
- **Entry Points**:
  - `lib/features/cart/presentation/pages/cart_screen.dart`
  - `lib/features/cart/presentation/providers/cart_provider.dart`
- **API Integration**:
  - Backend Cart API
- **Key Features**:
  - Add to cart
  - Update quantities
  - Remove items
  - Cart persistence
  - Real-time updates

### 4. Checkout Flow
```mermaid
sequenceDiagram
    User->>App: Initiates checkout
    App->>Backend: Checkout request
    Backend-->>App: Checkout URL
    App->>Browser: Open checkout URL
    Browser->>Shopify: Complete purchase
```

#### Implementation Details
- **Entry Point**: `lib/features/cart/presentation/pages/cart_screen.dart`
- **API Integration**:
  - Backend Checkout API
  - Shopify Checkout
- **Key Features**:
  - Cart validation
  - Checkout URL generation
  - Order tracking
  - Purchase confirmation

## System Architecture

### 1. Frontend (Flutter)
- **State Management**: Riverpod
- **Navigation**: GoRouter
- **Local Storage**: Flutter Secure Storage
- **API Clients**:
  - Dio (REST)
  - GraphQL (Shopify)

### 2. Backend Services
- **Authentication**:
  - Firebase Auth
  - JWT-based backend auth
- **Product Management**: Shopify Storefront API
- **Cart Management**: Custom backend API
- **Checkout**: Shopify Checkout

### 3. Data Flow
1. **Product Listing**:
   - Shopify API → Backend Cache → App
2. **Authentication**:
   - Firebase → Backend → App
3. **Cart Operations**:
   - App → Backend → Shopify → App
4. **Checkout**:
   - App → Backend → Shopify Checkout

## Security Considerations

### 1. Authentication
- Firebase token validation
- JWT token management
- Secure token storage
- Session management

### 2. Data Protection
- HTTPS for all API calls
- Secure storage for sensitive data
- Input validation
- Rate limiting

### 3. API Security
- Token-based authentication
- Request signing
- CORS policies
- API key management

## Error Handling

### 1. User-Facing Errors
- Network connectivity issues
- Authentication failures
- Cart operation errors
- Checkout failures

### 2. System Errors
- API service unavailability
- Token validation failures
- Data synchronization issues
- Cache inconsistencies

## Dependencies
- `firebase_auth`: Authentication
- `google_sign_in`: Google Sign-In
- `dio`: REST API client
- `graphql_flutter`: Shopify API
- `flutter_secure_storage`: Secure storage
- `riverpod`: State management
- `go_router`: Navigation
- `freezed`: Data models

## Related Documentation
- [Authentication Feature Design](../features/auth/design.md)
- [Cart Feature Design](../features/cart/design.md)
- [API Documentation](../references/travelgator_api.postman_collection.json)