# Flutter Travelgator Architecture

## Project Structure

```
lib/
├── core/                    # Core functionality and utilities
│   ├── config/             # App configuration
│   ├── constants/          # App-wide constants
│   ├── errors/             # Error handling and custom exceptions
│   ├── network/            # Network layer (Dio, GraphQL client)
│   ├── theme/              # App theming
│   └── utils/              # Utility functions and helpers
│
├── features/               # Feature-based modules
│   ├── auth/              # Authentication feature
│   │   ├── data/         # Data layer
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   ├── domain/       # Domain layer
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── usecases/
│   │   └── presentation/ # Presentation layer
│   │       ├── pages/
│   │       ├── providers/
│   │       └── widgets/
│   │
│   ├── cart/             # Shopping cart feature
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   │
│   └── [other_features]/ # Other features following same structure
│
├── shared/                # Shared widgets and components
│   ├── widgets/          # Reusable widgets
│   └── layouts/          # Layout templates
│
└── main.dart             # App entry point
```

## Architecture Patterns

### Clean Architecture
The project follows Clean Architecture principles with three main layers:

1. **Presentation Layer**
   - UI components (pages, widgets)
   - State management (Riverpod)
   - View models/providers
   - User interactions

2. **Domain Layer**
   - Business logic
   - Use cases
   - Entities
   - Repository interfaces

3. **Data Layer**
   - Repository implementations
   - Data sources (remote, local)
   - Models
   - API clients

### State Management
- **Riverpod** for state management
- Immutable state objects
- Async state handling
- Dependency injection

### Dependency Injection
- Riverpod for DI
- Provider scoping
- Repository pattern
- Use case injection

### Error Handling
- Custom error types
- Error boundaries
- Global error handling
- User-friendly error messages

### Network Layer
- Dio for REST APIs
- GraphQL client for Shopify
- Interceptors for auth
- Error handling
- Request/response logging

### Local Storage
- SharedPreferences for simple data
- Hive for complex data
- Secure storage for sensitive data

## Code Organization

### Feature Modules
Each feature module is self-contained and follows the same structure:

```
feature/
├── data/
│   ├── datasources/      # API clients, local storage
│   ├── models/          # Data models (freezed)
│   └── repositories/    # Repository implementations
│
├── domain/
│   ├── entities/        # Business objects
│   ├── repositories/    # Repository interfaces
│   └── usecases/       # Business logic
│
└── presentation/
    ├── pages/          # Full screens
    ├── providers/      # State management
    └── widgets/        # UI components
```

### Naming Conventions
- Files: snake_case.dart
- Classes: PascalCase
- Variables/functions: camelCase
- Constants: SCREAMING_SNAKE_CASE
- Private members: _camelCase

### Code Style
- Follow Flutter style guide
- Use dart_code_metrics
- Maximum line length: 80
- Use trailing commas
- Sort imports

## Testing Strategy

### Unit Tests
- Test use cases
- Test repositories
- Test providers
- Mock dependencies

### Widget Tests
- Test UI components
- Test user interactions
- Test state changes

### Integration Tests
- Test feature flows
- Test API integration
- Test navigation

## Build and Deployment

### Environment Configuration
- Development
- Staging
- Production

### Build Modes
- Debug
- Profile
- Release

### CI/CD
- GitHub Actions
- Automated testing
- Code quality checks
- Build verification

## Performance Considerations

### App Size
- Asset optimization
- Code splitting
- Lazy loading

### Runtime Performance
- Widget optimization
- Memory management
- Network caching
- Image optimization

### Build Performance
- Incremental builds
- Build caching
- Parallel testing