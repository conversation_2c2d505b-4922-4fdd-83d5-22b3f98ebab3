{"info": {"_postman_id": "f727f9fe-6df7-49eb-a8f1-5fea340364e4", "name": "Travelgator API - Auth & Cart Collection", "description": "Collection for Firebase authentication and shopping cart operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12511357"}, "item": [{"name": "Authentication", "item": [{"name": "Authenticate with Firebase", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('appToken', response.token);", "    }", "    if (response.user && response.user.id) {", "        pm.collectionVariables.set('userId', response.user.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{firebaseToken}}"}], "url": {"raw": "{{baseUrl}}/auth/authenticate", "host": ["{{baseUrl}}"], "path": ["auth", "authenticate"]}, "description": "Authenticate using Firebase ID token and get app JWT token"}, "response": []}]}, {"name": "Cart Management", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{appToken}}"}], "url": {"raw": "{{baseUrl}}/cart", "host": ["{{baseUrl}}"], "path": ["cart"]}, "description": "Get current user's active cart"}, "response": []}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{appToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"variant_id\": \"123456789\",\n  \"product_id\": \"987654321\",\n  \"quantity\": 2,\n  \"price\": 29.99,\n  \"title\": \"Sample Product\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/add_item", "host": ["{{baseUrl}}"], "path": ["cart", "add_item"]}, "description": "Add a product variant to the cart"}, "response": []}, {"name": "Update Cart Item", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{appToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"variant_id\": \"123456789\",\n  \"quantity\": 3\n}"}, "url": {"raw": "{{baseUrl}}/cart/update_item", "host": ["{{baseUrl}}"], "path": ["cart", "update_item"]}, "description": "Update quantity of an item in the cart"}, "response": []}, {"name": "Remove Item from Cart", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{appToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"variant_id\": \"123456789\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/remove_item", "host": ["{{baseUrl}}"], "path": ["cart", "remove_item"]}, "description": "Remove a specific item from the cart"}, "response": []}, {"name": "Checkout <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{appToken}}"}], "url": {"raw": "{{baseUrl}}/cart/checkout", "host": ["{{baseUrl}}"], "path": ["cart", "checkout"]}, "description": "Create Shopify checkout and order from cart items"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Add any global pre-request scripts here"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Add any global test scripts here"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "firebaseToken", "value": "", "type": "string"}, {"key": "appToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}]}