# Authentication Feature Design

## Overview
The authentication feature handles user authentication and session management in the Travelgator app. It implements Google Sign-In as the primary authentication method with Firebase integration, requiring both Firebase and backend authentication for a complete sign-in.

> **Note**: This feature is part of the larger application architecture. For a complete understanding of how authentication fits into the overall user flows, please refer to the [User Flows Architecture](../../architecture/user_flows.md) document.

## References

### Pages
- `lib/features/auth/presentation/screens/sign_in_screen.dart` - Sign in screen

### Models
- `lib/features/auth/domain/entities/user.dart` - User entity
- `lib/features/auth/data/models/user_model.dart` - User data model
- `lib/features/auth/presentation/providers/auth_state.dart` - Auth state management

### API Endpoints
See `knowledge_base/references/postman.json` for detailed API documentation:
- `POST /api/auth/login` - User login with Firebase token
- `POST /api/auth/refresh-token` - Refresh auth token

## User Flows

### 1. Google Sign-In Flow
1. User taps "Add to Cart" on a product
2. App prompts for login
3. User taps "Sign in with Google" button
4. System authenticates with Google
5. Firebase authentication is performed
   - Must succeed to proceed
   - Returns Firebase user data and ID token
6. Backend authentication is performed
   - Required step (no fallback)
   - Uses Firebase ID token
   - Must succeed for complete authentication
7. On success:
   - Store tokens securely
   - Update auth state
   - Return to cart operation
8. On error:
   - Show error message
   - Allow retry

### 2. Session Management Flow
1. App checks for stored tokens on startup
2. If tokens exist:
   - Validate token expiration
   - If expired: Attempt refresh
   - If refresh fails: Logout
3. If no tokens:
   - Show sign in screen when needed
4. On token refresh:
   - Call refresh token API
   - Update stored tokens
   - Continue session

## State Management

### Auth States
- `initial`: App startup state
- `loading`: During authentication operations
- `authenticated`: User successfully logged in
- `unauthenticated`: User logged out
- `error`: Authentication operation failed

### Token Management
- Store tokens in secure storage using `flutter_secure_storage`
- Refresh token before expiration
- Clear tokens on logout
- Handle token refresh failures

## Implementation Structure

### 1. Domain Layer
- Core business logic
- User entity
- Repository interfaces
- Authentication use cases

### 2. Data Layer
- Firebase integration
- Backend API integration
- Data models
- Repository implementations
- Error handling

### 3. Presentation Layer
- Sign-in screen
- Google Sign-In button
- Error handling and user feedback
- Loading states

## Error Handling

### Common Error Scenarios
- Network connectivity issues
- Firebase authentication failures
- Backend service unavailability
- Token validation failures

### Error Response Format
```json
{
  "error": {
    "code": "string",
    "message": "string",
    "details": {}
  }
}
```

## Dependencies
- `firebase_auth`: Firebase authentication
- `google_sign_in`: Google Sign-In integration
- `flutter_secure_storage`: Secure token storage
- `dio`: API client
- `freezed`: Data models
- `riverpod`: State management