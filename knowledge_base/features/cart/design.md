# Shopping Cart Feature Design

## Overview
The shopping cart feature manages the user's shopping cart, including adding/removing items, updating quantities, and proceeding to checkout. It integrates with both our backend API and Shopify's Storefront API, requiring user authentication for cart operations.

> **Note**: This feature is part of the larger application architecture. For a complete understanding of how the cart feature fits into the overall user flows, including product listing and checkout, please refer to the [User Flows Architecture](../../architecture/user_flows.md) document.

## References

### Pages
- `lib/features/cart/presentation/pages/cart_screen.dart` - Cart screen

### Models
- `lib/features/cart/domain/entities/cart.dart` - Cart entity
- `lib/features/cart/data/models/cart_model.dart` - Cart data model
- `lib/features/cart/presentation/providers/cart_state.dart` - Cart state management

### API Endpoints
See `knowledge_base/references/postman.json` for detailed API documentation:

#### Backend REST API
- `GET /api/cart` - Get cart
- `POST /api/cart/add_item` - Add item to cart
- `PATCH /api/cart/update_item` - Update cart item
- `DELETE /api/cart/remove_item` - Remove cart item
- `POST /api/cart/checkout` - Proceed to checkout

## User Flows

### 1. Add to Cart Flow
1. User views product listing from Shopify API
2. User selects product variant
3. User taps "Add to Cart" button
4. If not authenticated:
   - Prompt for login
   - Complete Google Sign-In flow
   - Return to cart operation
5. App calls add item API
6. On success:
   - Update cart state
   - Show success message
   - Update cart badge
7. On error:
   - Show error message
   - Allow retry

### 2. Update Cart Flow
1. User views cart screen
2. User modifies item quantity
3. App calls update item API
4. On success:
   - Update cart state
   - Update total price
5. On error:
   - Revert quantity
   - Show error message

### 3. Remove Item Flow
1. User views cart screen
2. User taps remove button
3. App calls remove item API
4. On success:
   - Update cart state
   - Update total price
5. On error:
   - Show error message
   - Allow retry

### 4. Checkout Flow
1. User views cart screen
2. User taps checkout button
3. App calls checkout API
4. On success:
   - Get checkout URL
   - Open checkout in browser
5. On error:
   - Show error message
   - Allow retry

## State Management

### Cart States
- `initial`: App startup state
- `loading`: During cart operations
- `loaded`: Cart data loaded
- `error`: Operation failed

### Cart Operations
- Add item to cart
- Update item quantity
- Remove item from cart
- Proceed to checkout

## Implementation Structure

### 1. Domain Layer
- Core business logic
- Cart entity
- Repository interfaces
- Cart operation use cases

### 2. Data Layer
- Backend API integration
- Data models
- Repository implementations
- Error handling

### 3. Presentation Layer
- Cart screen
- Cart item widgets
- Loading states
- Error handling and user feedback

## Error Handling

### Common Error Scenarios
- Network connectivity issues
- Authentication required
- Invalid item quantities
- Out of stock items
- Checkout failures
- API service unavailability

### Error Response Format
```json
{
  "error": {
    "code": "string",
    "message": "string",
    "details": {}
  }
}
```

## Dependencies
- `dio`: REST API client
- `freezed`: Data models
- `riverpod`: State management
- `flutter_secure_storage`: Cart persistence
- `url_launcher`: Checkout URL handling