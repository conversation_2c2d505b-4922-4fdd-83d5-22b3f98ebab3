# TravelGator - Flutter App

A travel application built with Flutter and Riverpod, following clean architecture principles.

## Project Structure

```
lib/
├── main.dart               # Entry point
├── app.dart                # App configuration
├── core/                   # Core functionality
│   ├── constants/          # App constants
│   ├── exceptions/         # Custom exceptions
│   ├── theme/              # App theming
│   └── utils/              # Utility functions
├── features/               # Feature modules
│   ├── auth/               # Authentication feature
│   │   ├── data/           # Data layer (repositories, models)
│   │   ├── domain/         # Domain layer (entities, usecases)
│   │   ├── presentation/   # UI layer (providers, screens, widgets)
│   │   └── auth_route.dart # Feature routes
│   ├── home/               # Home feature
│   │   └── ...
├── l10n/                   # Localization
├── routes/                 # App routing
│   └── app_router.dart
└── shared/                 # Shared code
    ├── widgets/            # Common widgets
    └── providers/          # Global providers
```

## Architecture

This project follows a clean architecture approach with a feature-based organization, ensuring separation of concerns and maintainable code. The architecture is divided into three main layers:

### 1. Data Layer (`data/`)
- **Repositories**: Implements the repository interfaces defined in the domain layer
- **Data Sources**:
  - Remote Data Sources: API clients, network calls
  - Local Data Sources: SharedPreferences, SQLite, Hive
- **Data Models**: DTOs (Data Transfer Objects) for API and local storage
- **Mappers**: Convert between data models and domain entities

### 2. Domain Layer (`domain/`)
The domain layer is the core of the application, containing business logic and rules. It is independent of any framework or external concerns.

#### Key Components:
- **Entities**: Core business objects (e.g., `User`, `Trip`, `Destination`)
- **Repository Interfaces**: Abstract contracts defining data operations
- **Use Cases**: Business logic implementation
  - Single Responsibility Principle: Each use case handles one specific action
  - Examples:
    - `GetUserProfileUseCase`
    - `CreateTripUseCase`
    - `SearchDestinationsUseCase`
- **Value Objects**: Immutable objects representing domain concepts
- **Failures**: Custom error types for domain-specific errors

#### Domain Layer Benefits:
1. **Independence**: Framework and UI agnostic
2. **Testability**: Business logic can be tested in isolation
3. **Maintainability**: Clear separation of concerns
4. **Reusability**: Domain logic can be reused across different UIs

### 3. Presentation Layer (`presentation/`)
- **Screens**: UI pages and views
- **Widgets**: Reusable UI components
- **State Management**:
  - Providers: Using Riverpod for state management
  - Controllers: Business logic for UI
  - State Classes: Immutable state objects

### Layer Communication Flow:
```
UI (Presentation) → Use Cases (Domain) → Repositories (Domain) → Data Sources (Data)
```

### Dependency Rule:
- Inner layers (Domain) have no knowledge of outer layers
- Dependencies point inward
- Domain layer has no dependencies on Data or Presentation layers

## State Management

The app uses Riverpod for state management with the following provider types:

- **Provider**: For simple dependencies and computations
- **StateProvider**: For simple state that can be modified from outside
- **StateNotifierProvider**: For complex state with reducers
- **FutureProvider**: For async data fetching

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run the app with `flutter run`

## Features

- Authentication (sign-in, sign-up)
- Theme management
- Clean architecture implementation
- Feature-based project organization

## Dependencies

- flutter_riverpod: State management
- equatable: Value equality
- dartz: Functional programming
- google_fonts: Typography
- font_awesome_flutter: Icons
