import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'firebase_options.dart';
import 'app.dart';
import 'core/configs/graphql_config.dart';
import 'core/providers/logger.dart';

// Global provider for Firebase initialization state
final firebaseInitProvider = Provider<bool>((ref) {
  return false; // Will be set to true once Firebase is initialized
});

Future<bool> _initializeFirebase() async {
  try {
    debugPrint('Attempting to initialize Firebase...');

    // Check if Firebase is already initialized
    if (Firebase.apps.isEmpty) {
      debugPrint(
        'No Firebase apps found, initializing with DefaultFirebaseOptions.currentPlatform',
      );
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      debugPrint('Firebase initialized successfully: ${Firebase.app().name}');
      debugPrint('Firebase projectId: ${Firebase.app().options.projectId}');
      debugPrint('Firebase appId: ${Firebase.app().options.appId}');
    } else {
      debugPrint('Firebase already initialized: ${Firebase.app().name}');
      debugPrint('Firebase projectId: ${Firebase.app().options.projectId}');
      debugPrint('Firebase appId: ${Firebase.app().options.appId}');
    }
    return true;
  } on FirebaseException catch (e, stackTrace) {
    debugPrint('Firebase initialization failed with FirebaseException: $e');
    debugPrint('Error code: ${e.code}, message: ${e.message}');
    debugPrint('Stack trace: $stackTrace');
    return false;
  } on PlatformException catch (e, stackTrace) {
    debugPrint('Firebase initialization failed with PlatformException: $e');
    debugPrint('Error code: ${e.code}, message: ${e.message}');
    debugPrint('Stack trace: $stackTrace');
    return false;
  } catch (e, stackTrace) {
    debugPrint('Firebase initialization failed with unexpected error: $e');
    debugPrint('Stack trace: $stackTrace');
    return false;
  }
}

void main() async {
  // Ensure Flutter is initialized first
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for GraphQL cache
  await initHiveForFlutter();

  // Try to initialize Firebase - if it fails, continue without it
  final bool firebaseInitialized = await _initializeFirebase();

  if (!firebaseInitialized) {
    debugPrint('Firebase is not initialized, auth features will be disabled');
  }

  // Create a ProviderContainer that's used throughout the app
  final container = ProviderContainer(
    overrides: [
      // Override the firebase init provider with the actual initialization state
      firebaseInitProvider.overrideWithValue(firebaseInitialized),
    ],
    observers: [Logger()],
  );

  runApp(
    UncontrolledProviderScope(
      container: container,
      child: GraphQLConfig.wrapWithClient(child: const App()),
    ),
  );
}
