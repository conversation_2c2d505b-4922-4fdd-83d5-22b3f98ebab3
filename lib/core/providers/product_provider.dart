import 'package:flutter_travelgator/features/home/<USER>/models/region.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import '../configs/graphql_config.dart';
import '../models/product_model.dart';
import '../repositories/product_repository.dart';
import '../../features/home/<USER>/models/country.dart';

// GraphQL client provider
final graphQLClientProvider = Provider<GraphQLClient>((ref) {
  return GraphQLConfig.initClient().value;
});

// Product repository provider
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  final client = ref.watch(graphQLClientProvider);
  return ProductRepository(client: client);
});

// Products state class
class ProductsState {
  final List<Product> products;
  final bool isLoading;
  final String? error;
  final bool hasNextPage;
  final String? endCursor;
  final String? activeRegion;
  final String? activeCountry;

  ProductsState({
    this.products = const [],
    this.isLoading = false,
    this.error,
    this.hasNextPage = false,
    this.endCursor,
    this.activeRegion,
    this.activeCountry,
  });

  ProductsState copyWith({
    List<Product>? products,
    bool? isLoading,
    String? error,
    bool? hasNextPage,
    String? endCursor,
    String? activeRegion,
    String? activeCountry,
  }) {
    return ProductsState(
      products: products ?? this.products,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      endCursor: endCursor ?? this.endCursor,
      activeRegion: activeRegion ?? this.activeRegion,
      activeCountry: activeCountry ?? this.activeCountry,
    );
  }
}

// Products notifier
class ProductsNotifier extends StateNotifier<ProductsState> {
  final ProductRepository _repository;

  ProductsNotifier(this._repository) : super(ProductsState());

  Future<void> fetchProducts({int first = 10, String? queryText}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final productsConnection = await _repository.getProductsWithPagination(
        first: first,
        query: queryText,
      );

      final products =
          productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Force refresh products from network, skipping any cache
  Future<void> forceRefreshProducts({int first = 5}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Clear GraphQL cache for this query
      await _repository.clearProductCache();

      final productsConnection = await _repository.getProductsWithPagination(
        first: first,
        fetchPolicy: FetchPolicy.networkOnly, // Force network fetch
      );

      final products =
          productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: products,
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> fetchMoreProducts({int first = 10, String? queryText}) async {
    if (!state.hasNextPage || state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);

      final productsConnection = await _repository.getProductsWithPagination(
        first: first,
        query: queryText,
        after: state.endCursor,
      );

      final newProducts =
          productsConnection.edges.map((edge) => edge.node).toList();

      state = state.copyWith(
        products: [...state.products, ...newProducts],
        isLoading: false,
        hasNextPage: productsConnection.pageInfo.hasNextPage,
        endCursor: productsConnection.pageInfo.endCursor,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Set active region and fetch products
  Future<void> setRegionFilter(String? region) async {
    state = state.copyWith(
      activeRegion: region,
      activeCountry: null, // Clear country filter when setting region
      isLoading: true,
      error: null,
      products: [],
      hasNextPage: false,
      endCursor: null,
    );
  }

  /// Set active country and fetch products
  Future<void> setCountryFilter(String? country) async {
    state = state.copyWith(
      activeCountry: country,
      isLoading: true,
      error: null,
      products: [],
      hasNextPage: false,
      endCursor: null,
    );
  }

  Future<void> clearAndFetchProducts() async {
    if (state.activeRegion != null) {
      await setRegionFilter(null);
    }
    if (state.activeCountry != null) {
      await setCountryFilter(null);
    }
    // Reset all state to initial values
    state = ProductsState(
      products: [],
      isLoading: true,
      error: null,
      hasNextPage: false,
      endCursor: null,
      activeRegion: null,
      activeCountry: null,
    );

    // Force refresh products with no cache, exactly like first run
    await forceRefreshProducts(first: 5);
  }
}

// Product state provider
final productsProvider = StateNotifierProvider<ProductsNotifier, ProductsState>(
  (ref) {
    final repository = ref.watch(productRepositoryProvider);
    return ProductsNotifier(repository);
  },
);

/// Provider for regions
final regionsProvider = FutureProvider<List<Region>>((ref) async {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getRegions();
});

/// Provider for countries
final countriesProvider = FutureProvider<List<Country>>((ref) async {
  final repository = ref.watch(productRepositoryProvider);
  return repository.getCountries();
});
