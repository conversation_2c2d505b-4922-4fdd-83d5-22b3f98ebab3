// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tab_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tabStateHash() => r'f877f00ea19ef0db6553f15d820e376efa974dac';

/// Provider for managing the current tab index using Riverpod's code generation
///
/// Copied from [TabState].
@ProviderFor(TabState)
final tabStateProvider = AutoDisposeNotifierProvider<TabState, int>.internal(
  TabState.new,
  name: r'tabStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$tabStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TabState = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
