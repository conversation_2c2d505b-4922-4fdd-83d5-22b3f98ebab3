import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/home/<USER>/models/region.dart';
import '../repositories/product_repository.dart';
import 'product_provider.dart';

// Regions state class
class RegionsState {
  final List<Region> regions;
  final bool isLoading;
  final String? error;
  final String? activeRegion;

  RegionsState({
    this.regions = const [],
    this.isLoading = false,
    this.error,
    this.activeRegion,
  });

  RegionsState copyWith({
    List<Region>? regions,
    bool? isLoading,
    String? error,
    String? activeRegion,
  }) {
    return RegionsState(
      regions: regions ?? this.regions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      activeRegion: activeRegion ?? this.activeRegion,
    );
  }
}

// Regions notifier
class RegionsNotifier extends StateNotifier<RegionsState> {
  final ProductRepository _repository;

  RegionsNotifier(this._repository) : super(RegionsState());

  Future<void> fetchRegions() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Fetch regions from repository
      final regions = await _repository.getRegions();

      state = state.copyWith(regions: regions, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Force refresh regions from network, skipping any cache
  Future<void> forceRefreshRegions() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Clear GraphQL cache for this query (if applicable)
      await _repository.clearProductCache();

      final regions = await _repository.getRegions();

      state = state.copyWith(regions: regions, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Set active region
  void setActiveRegion(String? region) {
    state = state.copyWith(activeRegion: region);
  }

  /// Clear active region
  void clearActiveRegion() {
    state = state.copyWith(activeRegion: null);
  }
}

// Regions state provider
final regionsStateProvider =
    StateNotifierProvider<RegionsNotifier, RegionsState>((ref) {
      final repository = ref.watch(productRepositoryProvider);
      return RegionsNotifier(repository);
    });
