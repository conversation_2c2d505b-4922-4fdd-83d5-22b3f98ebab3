import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/home/<USER>/models/country.dart';
import '../repositories/product_repository.dart';
import 'product_provider.dart';

// Countries state class
class CountriesState {
  final List<Country> countries;
  final bool isLoading;
  final String? error;
  final String? activeCountry;

  CountriesState({
    this.countries = const [],
    this.isLoading = false,
    this.error,
    this.activeCountry,
  });

  CountriesState copyWith({
    List<Country>? countries,
    bool? isLoading,
    String? error,
    String? activeCountry,
  }) {
    return CountriesState(
      countries: countries ?? this.countries,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      activeCountry: activeCountry ?? this.activeCountry,
    );
  }
}

// Countries notifier
class CountriesNotifier extends StateNotifier<CountriesState> {
  final ProductRepository _repository;

  CountriesNotifier(this._repository) : super(CountriesState());

  Future<void> fetchCountries() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Fetch countries from repository
      final countries = await _repository.getCountries();

      state = state.copyWith(countries: countries, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Force refresh countries from network, skipping any cache
  Future<void> forceRefreshCountries() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Clear GraphQL cache for this query (if applicable)
      await _repository.clearProductCache();

      final countries = await _repository.getCountries();

      state = state.copyWith(countries: countries, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Set active country
  void setActiveCountry(String? country) {
    state = state.copyWith(activeCountry: country);
  }

  /// Clear active country
  void clearActiveCountry() {
    state = state.copyWith(activeCountry: null);
  }
}

// Countries state provider
final countriesStateProvider =
    StateNotifierProvider<CountriesNotifier, CountriesState>((ref) {
      final repository = ref.watch(productRepositoryProvider);
      return CountriesNotifier(repository);
    });
