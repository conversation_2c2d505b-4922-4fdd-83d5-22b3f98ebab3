class GraphQLQueries {
  static String getProducts = '''
    query Products(\$first: Int!, \$query: String, \$after: String) {
      products(first: \$first, query: \$query, after: \$after) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            handle
            featuredImage {
              url
              altText
            }
            variants(first: 1) {
              edges {
                node {
                  id
                  price {
                    amount
                    currencyCode
                  }
                  compareAtPrice {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
        }
      }
    }
  ''';

  static String getProductsByRegionAndCountry = '''
    query ProductsByRegionAndCountry(
      \$first: Int!,
      \$after: String,
      \$region: String,
      \$country: String
    ) {
      products(
        first: \$first,
        after: \$after,
        query: \$region != "" ? "tag:region-\${region}" : "" + \$country != "" ? " tag:country-\${country}" : ""
      ) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            handle
            tags
            metafields(first: 10) {
              edges {
                node {
                  key
                  value
                  namespace
                }
              }
            }
            featuredImage {
              url
              altText
            }
            variants(first: 1) {
              edges {
                node {
                  id
                  price {
                    amount
                    currencyCode
                  }
                  compareAtPrice {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
        }
      }
    }
  ''';

  static String getRegions = '''
    query Regions {
      metaobjects(type: "region", first: 20) {
        nodes {
          id
          handle
          fields {
            key
            value
          }
        }
      }
    }
  ''';

  static String getCountries = '''
    query Countries {
      metaobjects(type: "country", first: 50) {
        nodes {
          id
          handle
          fields {
            key
            value
          }
        }
      }
    }
  ''';
}
