import 'package:flutter/foundation.dart';

/// Configuration for Apple Sign-In server-side authentication
/// 
/// This class manages the Apple Developer credentials needed for
/// server-side Apple authentication and JWT token generation.
class AppleAuthConfig {
  /// Apple Developer Team ID
  /// Found in Apple Developer Console -> Membership
  static const String teamId = String.fromEnvironment(
    'APPLE_TEAM_ID',
    defaultValue: '', // Add your Team ID here or via environment
  );

  /// Apple Sign-In Key ID
  /// Created in Apple Developer Console -> Certificates, Identifiers & Profiles -> Keys
  static const String keyId = String.fromEnvironment(
    'APPLE_KEY_ID',
    defaultValue: '', // Add your Key ID here or via environment
  );

  /// Apple Sign-In Service ID (Client ID)
  /// This should match your app's bundle identifier or a configured service ID
  static const String clientId = String.fromEnvironment(
    'APPLE_CLIENT_ID',
    defaultValue: 'com.travelgator.development', // Your app's bundle ID
  );

  /// Apple's token endpoint for server-to-server authentication
  static const String tokenEndpoint = 'https://appleid.apple.com/auth/token';

  /// Apple's public keys endpoint for token verification
  static const String publicKeysEndpoint = 'https://appleid.apple.com/auth/keys';

  /// Apple's authorization endpoint
  static const String authorizationEndpoint = 'https://appleid.apple.com/auth/authorize';

  /// Private key content (P8 format)
  /// In production, this should be loaded securely from environment or secure storage
  static const String privateKey = String.fromEnvironment(
    'APPLE_PRIVATE_KEY',
    defaultValue: '', // Your .p8 private key content
  );

  /// Validate that all required configuration is present
  static bool get isConfigured {
    return teamId.isNotEmpty && 
           keyId.isNotEmpty && 
           clientId.isNotEmpty && 
           privateKey.isNotEmpty;
  }

  /// Get configuration status for debugging
  static Map<String, dynamic> get configStatus {
    return {
      'teamId': teamId.isNotEmpty ? 'configured' : 'missing',
      'keyId': keyId.isNotEmpty ? 'configured' : 'missing',
      'clientId': clientId.isNotEmpty ? 'configured' : 'missing',
      'privateKey': privateKey.isNotEmpty ? 'configured' : 'missing',
      'isFullyConfigured': isConfigured,
    };
  }

  /// Debug configuration (safe for logging)
  static void debugConfiguration() {
    if (kDebugMode) {
      debugPrint('=== APPLE AUTH CONFIG DEBUG ===');
      debugPrint('Team ID: ${teamId.isNotEmpty ? "✓ Configured" : "✗ Missing"}');
      debugPrint('Key ID: ${keyId.isNotEmpty ? "✓ Configured" : "✗ Missing"}');
      debugPrint('Client ID: ${clientId.isNotEmpty ? "✓ Configured ($clientId)" : "✗ Missing"}');
      debugPrint('Private Key: ${privateKey.isNotEmpty ? "✓ Configured" : "✗ Missing"}');
      debugPrint('Fully Configured: ${isConfigured ? "✓ Yes" : "✗ No"}');
      debugPrint('=== END APPLE AUTH CONFIG ===');
    }
  }
}

/// Apple authentication scopes
class AppleAuthScopes {
  static const String email = 'email';
  static const String name = 'name';
  
  static const List<String> defaultScopes = [email, name];
}

/// Apple authentication response types
class AppleAuthResponseTypes {
  static const String code = 'code';
  static const String idToken = 'id_token';
}

/// Apple authentication grant types
class AppleAuthGrantTypes {
  static const String authorizationCode = 'authorization_code';
  static const String refreshToken = 'refresh_token';
}
