/// Environment configuration for the application
///
/// To set environment values during build, use:
/// flutter build apk --dart-define=API_URL=https://api.example.com
class Environment {
  /// Base URL for the API
  static const String apiUrl = String.fromEnvironment(
    'API_URL',
    defaultValue: 'https://api.dev.example.com',
  );

  /// API key for external services
  static const String apiKey = String.fromEnvironment(
    'API_KEY',
    defaultValue: '',
  );

  /// Flag indicating if the app is running in production
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');

  /// Flag indicating if debug logging is enabled
  static const bool enableLogging = !isProduction;

  /// Cache duration in minutes
  static const int cacheDuration = int.fromEnvironment(
    'CACHE_DURATION',
    defaultValue: 15,
  );
}
