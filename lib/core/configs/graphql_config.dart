import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

class GraphQLConfig {
  static final HttpLink httpLink = HttpLink(
    'https://1cnhcs-vy.myshopify.com/api/2025-04/graphql.json',
    defaultHeaders: {
      'X-Shopify-Storefront-Access-Token': '5d7582df7a19ac26c12af06c33cd0a0d',
      'Content-Type': 'application/json',
    },
  );

  static ValueNotifier<GraphQLClient> initClient() {
    return ValueNotifier(
      GraphQLClient(
        link: httpLink,
        cache: GraphQLCache(store: InMemoryStore()),
      ),
    );
  }

  static Widget wrapWithClient({required Widget child}) {
    return GraphQLProvider(client: initClient(), child: child);
  }
}
