import 'package:flutter_travelgator/features/home/<USER>/models/region.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import '../graphql/queries.dart';
import '../models/product_model.dart';
import '../../features/home/<USER>/models/country.dart';

class ProductRepository {
  final GraphQLClient client;

  ProductRepository({required this.client});

  Future<List<Product>> getProducts({
    int first = 10,
    String? query,
    String? after,
  }) async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProducts),
        variables: {'first': first, 'query': query, 'after': after},
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      final ProductsConnection productsConnection = ProductsConnection.fromJson(
        result.data?['products'],
      );

      return productsConnection.edges.map((edge) => edge.node).toList();
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  Future<ProductsConnection> getProductsWithPagination({
    int first = 10,
    String? query,
    String? after,
    FetchPolicy? fetchPolicy,
  }) async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProducts),
        variables: {'first': first, 'query': query, 'after': after},
        fetchPolicy: fetchPolicy ?? FetchPolicy.cacheFirst,
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      return ProductsConnection.fromJson(result.data?['products']);
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  /// Fetch products filtered by region and/or country
  Future<List<Product>> getProductsByRegionAndCountry({
    int first = 10,
    String? after,
    String? region,
    String? country,
  }) async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': first,
          'after': after,
          'region': region ?? "",
          'country': country ?? "",
        },
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      final ProductsConnection productsConnection = ProductsConnection.fromJson(
        result.data?['products'],
      );

      return productsConnection.edges.map((edge) => edge.node).toList();
    } catch (e) {
      throw Exception('Failed to fetch products by region and country: $e');
    }
  }

  /// Fetch products with pagination, filtered by region and/or country
  Future<ProductsConnection> getProductsByRegionAndCountryWithPagination({
    int first = 10,
    String? after,
    String? region,
    String? country,
  }) async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getProductsByRegionAndCountry),
        variables: {
          'first': first,
          'after': after,
          'region': region ?? "",
          'country': country ?? "",
        },
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      return ProductsConnection.fromJson(result.data?['products']);
    } catch (e) {
      throw Exception('Failed to fetch products by region and country: $e');
    }
  }

  /// Fetch all available regions
  Future<List<Region>> getRegions() async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getRegions),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      final regions = result.data?['metaobjects']['nodes'] as List;
      return regions.map<Region>((node) {
        final nodeData = node as Map<String, dynamic>;
        final fieldsList = nodeData['fields'] as List;
        final fields = Map<String, String>.fromEntries(
          fieldsList.map(
            (field) =>
                MapEntry(field['key'] as String, field['value'] as String),
          ),
        );

        return Region(
          name: fields['name'] ?? '',
          description: fields['description'] ?? '',
          code: fields['code'] ?? '',
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch regions: $e');
    }
  }

  /// Fetch all available countries and convert to Country model
  Future<List<Country>> getCountries() async {
    try {
      final QueryOptions options = QueryOptions(
        document: gql(GraphQLQueries.getCountries),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        throw Exception(result.exception.toString());
      }

      // Use the static fromShopifyList method to parse the response
      return Country.fromShopifyList(result.data!);
    } catch (e) {
      throw Exception('Failed to fetch countries: $e');
    }
  }

  /// Clear product cache to force fresh data fetch
  Future<void> clearProductCache() async {
    try {
      await client.resetStore();
    } catch (e) {
      throw Exception('Failed to clear product cache: $e');
    }
  }
}
