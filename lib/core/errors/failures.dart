import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const Failure({
    required this.message,
    this.code,
    this.details,
  });

  @override
  List<Object?> get props => [message, code, details];

  @override
  String toString() => 'Failure(message: $message, code: $code, details: $details)';
}

/// Failure for server-related errors
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// Failure for authentication-related errors
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// Failure for cart-related errors
class CartFailure extends Failure {
  const CartFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// Failure for network-related errors
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
    super.details,
  });
}

/// Failure related to cache operations
class CacheFailure extends Failure {
  const CacheFailure({
    super.message = 'Cache operation failed',
    super.code,
    super.details,
  });
}

/// Failure when data is not found
class NotFoundFailure extends Failure {
  const NotFoundFailure({
    super.message = 'Data not found',
    super.code,
    super.details,
  });
}

/// Failure related to validation errors
class ValidationFailure extends Failure {
  final Map<String, List<String>>? errors;

  const ValidationFailure({
    super.message = 'Validation failed',
    super.code,
    super.details,
    this.errors,
  });

  @override
  List<Object?> get props => [message, code, details, errors];
}

/// Unknown failures that don't fit other categories
class UnknownFailure extends Failure {
  const UnknownFailure({
    super.message = 'Unknown error occurred',
    super.code,
    super.details,
  });
}
