/// Base class for all exceptions
class AppException implements Exception {
  final String message;

  const AppException(this.message);

  @override
  String toString() => message;
}

/// Exception for authentication errors
class AuthException extends AppException {
  const AuthException(super.message);
}

/// Exception for server errors
class ServerException extends AppException {
  const ServerException(super.message);
}

/// Exception for network errors
class NetworkException extends AppException {
  const NetworkException(super.message);
}

/// Exception for cache errors
class CacheException extends AppException {
  const CacheException(super.message);
}
