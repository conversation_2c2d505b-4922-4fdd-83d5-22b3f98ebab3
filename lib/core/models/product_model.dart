class ProductsResponse {
  final ProductsConnection products;

  ProductsResponse({required this.products});

  factory ProductsResponse.fromJson(Map<String, dynamic> json) {
    return ProductsResponse(
      products: ProductsConnection.fromJson(json['products']),
    );
  }
}

class ProductsConnection {
  final PageInfo pageInfo;
  final List<ProductEdge> edges;

  ProductsConnection({required this.pageInfo, required this.edges});

  factory ProductsConnection.fromJson(Map<String, dynamic> json) {
    return ProductsConnection(
      pageInfo: PageInfo.fromJson(json['pageInfo']),
      edges:
          (json['edges'] as List)
              .map((edge) => ProductEdge.fromJson(edge))
              .toList(),
    );
  }
}

class PageInfo {
  final bool hasNextPage;
  final String? endCursor;

  PageInfo({required this.hasNextPage, this.endCursor});

  factory PageInfo.fromJson(Map<String, dynamic> json) {
    return PageInfo(
      hasNextPage: json['hasNextPage'],
      endCursor: json['endCursor'],
    );
  }
}

class ProductEdge {
  final Product node;

  ProductEdge({required this.node});

  factory ProductEdge.fromJson(Map<String, dynamic> json) {
    return ProductEdge(node: Product.fromJson(json['node']));
  }
}

class Product {
  final String id;
  final String title;
  final String handle;
  final FeaturedImage? featuredImage;
  final VariantsConnection variants;
  final String tags;
  final MetafieldsConnection? metafields;

  Product({
    required this.id,
    required this.title,
    required this.handle,
    this.featuredImage,
    required this.variants,
    this.tags = '',
    this.metafields,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      title: json['title'],
      handle: json['handle'],
      featuredImage:
          json['featuredImage'] != null
              ? FeaturedImage.fromJson(json['featuredImage'])
              : null,
      variants: VariantsConnection.fromJson(json['variants']),
      tags: json['tags'] as String? ?? '',
      metafields:
          json['metafields'] != null
              ? MetafieldsConnection.fromJson(json['metafields'])
              : null,
    );
  }

  // Helper methods to get region and country from tags or metafields
  String? get region {
    // First check tags (comma-separated string)
    if (tags.isNotEmpty) {
      final tagList = tags.split(',').map((tag) => tag.trim()).toList();
      for (final tag in tagList) {
        if (tag.startsWith('region-')) {
          return tag.substring(7);
        }
      }
    }

    // Then check metafields
    if (metafields != null) {
      for (final edge in metafields!.edges) {
        if (edge.node.namespace == 'travel' && edge.node.key == 'region') {
          return edge.node.value;
        }
      }
    }

    return null;
  }

  String? get country {
    // First check tags (comma-separated string)
    if (tags.isNotEmpty) {
      final tagList = tags.split(',').map((tag) => tag.trim()).toList();
      for (final tag in tagList) {
        if (tag.startsWith('country-')) {
          return tag.substring(8);
        }
      }
    }

    // Then check metafields
    if (metafields != null) {
      for (final edge in metafields!.edges) {
        if (edge.node.namespace == 'travel' && edge.node.key == 'country') {
          return edge.node.value;
        }
      }
    }

    return null;
  }
}

class FeaturedImage {
  final String url;
  final String? altText;

  FeaturedImage({required this.url, this.altText});

  factory FeaturedImage.fromJson(Map<String, dynamic> json) {
    return FeaturedImage(url: json['url'], altText: json['altText']);
  }
}

class VariantsConnection {
  final List<VariantEdge> edges;

  VariantsConnection({required this.edges});

  factory VariantsConnection.fromJson(Map<String, dynamic> json) {
    return VariantsConnection(
      edges:
          (json['edges'] as List)
              .map((edge) => VariantEdge.fromJson(edge))
              .toList(),
    );
  }
}

class VariantEdge {
  final Variant node;

  VariantEdge({required this.node});

  factory VariantEdge.fromJson(Map<String, dynamic> json) {
    return VariantEdge(node: Variant.fromJson(json['node']));
  }
}

class Variant {
  final String id;
  final MoneyV2 price;
  final MoneyV2? compareAtPrice;

  Variant({
    required this.id,
    required this.price,
    this.compareAtPrice,
  });

  factory Variant.fromJson(Map<String, dynamic> json) {
    return Variant(
      id: json['id'],
      price: MoneyV2.fromJson(json['price']),
      compareAtPrice: json['compareAtPrice'] != null
          ? MoneyV2.fromJson(json['compareAtPrice'])
          : null,
    );
  }
}



class MoneyV2 {
  final String amount;
  final String currencyCode;

  MoneyV2({required this.amount, required this.currencyCode});

  factory MoneyV2.fromJson(Map<String, dynamic> json) {
    return MoneyV2(amount: json['amount'], currencyCode: json['currencyCode']);
  }
}

// Helper methods to easily access price information
extension ProductPriceExtension on Product {
  MoneyV2? get price {
    if (variants.edges.isEmpty) return null;
    final variant = variants.edges.first.node;
    return variant.price;
  }

  MoneyV2? get compareAtPrice {
    if (variants.edges.isEmpty) return null;
    final variant = variants.edges.first.node;
    return variant.compareAtPrice;
  }
}

class MetafieldsConnection {
  final List<MetafieldEdge> edges;

  MetafieldsConnection({required this.edges});

  factory MetafieldsConnection.fromJson(Map<String, dynamic> json) {
    return MetafieldsConnection(
      edges:
          (json['edges'] as List)
              .map((edge) => MetafieldEdge.fromJson(edge))
              .toList(),
    );
  }
}

class MetafieldEdge {
  final Metafield node;

  MetafieldEdge({required this.node});

  factory MetafieldEdge.fromJson(Map<String, dynamic> json) {
    return MetafieldEdge(node: Metafield.fromJson(json['node']));
  }
}

class Metafield {
  final String key;
  final String value;
  final String namespace;

  Metafield({required this.key, required this.value, required this.namespace});

  factory Metafield.fromJson(Map<String, dynamic> json) {
    return Metafield(
      key: json['key'],
      value: json['value'],
      namespace: json['namespace'],
    );
  }
}
