class AppConstants {
  // App information
  static const String appName = 'TravelGator';
  static const String appVersion = '1.0.0';

  // API endpoints
  static const String baseUrl = 'https://api.example.com';
  static const String apiVersion = '/v1';
  static const String apiBaseUrl = baseUrl + apiVersion;

  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Local storage keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'app_theme';

  // Feature Flags
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;

  // Cache Configuration
  static const int cacheMaxAge = 3600; // 1 hour in seconds

  // Animation durations
  static const int shortAnimationDuration = 200; // milliseconds
  static const int mediumAnimationDuration = 400; // milliseconds
  static const int longAnimationDuration = 800; // milliseconds
}
