import 'package:flutter_travelgator/core/storage/local_storage.dart';
import 'package:get_it/get_it.dart';

/// Global ServiceLocator instance
final sl = GetIt.instance;

/// Setup all dependencies
Future<void> setupServiceLocator() async {
  // Core
  sl.registerLazySingleton<LocalStorage>(() => LocalStorageImpl());

  // Features can register their dependencies via extension methods
  // await setupAuthDependencies();
}
