import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Abstract interface for local storage operations
abstract class LocalStorage {
  /// Get a value from storage by key
  Future<T?> get<T>(String key);

  /// Save a value to storage with a key
  Future<bool> set<T>(String key, T value);

  /// Remove a value from storage by key
  Future<bool> remove(String key);

  /// Clear all values from storage
  Future<bool> clear();

  /// Check if a key exists in storage
  Future<bool> containsKey(String key);
}

/// Implementation of [LocalStorage] using SharedPreferences
class LocalStorageImpl implements LocalStorage {
  late SharedPreferences _prefs;
  final _initCompleter = Completer<void>();

  /// Initialize SharedPreferences
  LocalStorageImpl() {
    _init();
  }

  Future<void> _init() async {
    _prefs = await SharedPreferences.getInstance();
    if (!_initCompleter.isCompleted) {
      _initCompleter.complete();
    }
  }

  /// Ensure initialization is complete before operations
  Future<void> _ensureInitialized() async {
    if (!_initCompleter.isCompleted) {
      await _initCompleter.future;
    }
  }

  @override
  Future<T?> get<T>(String key) async {
    await _ensureInitialized();

    if (!await containsKey(key)) return null;

    // Handle different types accordingly
    if (T == String) {
      return _prefs.getString(key) as T?;
    } else if (T == int) {
      return _prefs.getInt(key) as T?;
    } else if (T == double) {
      return _prefs.getDouble(key) as T?;
    } else if (T == bool) {
      return _prefs.getBool(key) as T?;
    } else if (T == List<String>) {
      return _prefs.getStringList(key) as T?;
    } else {
      // For complex objects, try to decode from JSON
      final jsonString = _prefs.getString(key);
      if (jsonString != null) {
        try {
          return json.decode(jsonString) as T?;
        } catch (_) {
          return null;
        }
      }
      return null;
    }
  }

  @override
  Future<bool> set<T>(String key, T value) async {
    await _ensureInitialized();

    if (value == null) {
      return false;
    }

    if (value is String) {
      return _prefs.setString(key, value);
    } else if (value is int) {
      return _prefs.setInt(key, value);
    } else if (value is double) {
      return _prefs.setDouble(key, value);
    } else if (value is bool) {
      return _prefs.setBool(key, value);
    } else if (value is List<String>) {
      return _prefs.setStringList(key, value);
    } else {
      // For complex objects, serialize to JSON
      try {
        final jsonString = json.encode(value);
        return _prefs.setString(key, jsonString);
      } catch (_) {
        return false;
      }
    }
  }

  @override
  Future<bool> remove(String key) async {
    await _ensureInitialized();
    return _prefs.remove(key);
  }

  @override
  Future<bool> clear() async {
    await _ensureInitialized();
    return _prefs.clear();
  }

  @override
  Future<bool> containsKey(String key) async {
    await _ensureInitialized();
    return _prefs.containsKey(key);
  }
}
