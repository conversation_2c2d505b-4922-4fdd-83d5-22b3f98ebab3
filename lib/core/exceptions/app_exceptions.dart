class AppException implements Exception {
  final String message;
  final String? prefix;
  final String? url;

  AppException({required this.message, this.prefix, this.url});

  @override
  String toString() {
    return "$prefix$message";
  }
}

class ServerException extends AppException {
  ServerException({required super.message, super.url})
    : super(prefix: "Server Error: ");
}

class ClientException extends AppException {
  ClientException({required super.message, super.url})
    : super(prefix: "Client Error: ");
}

class BadRequestException extends AppException {
  BadRequestException({required super.message, super.url})
    : super(prefix: "Bad Request: ");
}

class UnauthorizedException extends AppException {
  UnauthorizedException({required super.message, super.url})
    : super(prefix: "Unauthorized: ");
}

class ForbiddenException extends AppException {
  ForbiddenException({required super.message, super.url})
    : super(prefix: "Forbidden: ");
}

class NotFoundException extends AppException {
  NotFoundException({required super.message, super.url})
    : super(prefix: "Not Found: ");
}

class NetworkException extends AppException {
  NetworkException({required super.message, super.url})
    : super(prefix: "Network Error: ");
}

class ConnectionException extends AppException {
  ConnectionException({required super.message, super.url})
    : super(prefix: "Connection Error: ");
}

class TimeoutException extends AppException {
  TimeoutException({required super.message, super.url})
    : super(prefix: "Timeout: ");
}

class CacheException extends AppException {
  CacheException({required super.message}) : super(prefix: "Cache Error: ");
}
