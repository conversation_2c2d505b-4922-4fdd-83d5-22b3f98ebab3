import 'package:flutter/foundation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

/// Helper class for debugging Apple Sign-In credentials and tokens
class AppleDebugHelper {
  /// Debug Apple Sign-In credential details
  static void debugAppleCredential(AuthorizationCredentialAppleID credential) {
    if (kDebugMode) {
      debugPrint('=== APPLE CREDENTIAL DEBUG ===');
      debugPrint('User ID: ${credential.userIdentifier ?? 'null'}');
      debugPrint('Email: ${credential.email ?? 'null'}');
      debugPrint('Given Name: ${credential.givenName ?? 'null'}');
      debugPrint('Family Name: ${credential.familyName ?? 'null'}');
      
      // Debug identity token
      final identityToken = credential.identityToken;
      if (identityToken != null) {
        debugPrint('Identity Token present: true');
        debugPrint('Identity Token length: ${identityToken.length}');
        if (identityToken.length > 50) {
          debugPrint('Identity Token (first 50 chars): ${identityToken.substring(0, 50)}...');
        } else {
          debugPrint('Identity Token: $identityToken');
        }
      } else {
        debugPrint('Identity Token present: false');
      }
      
      // Debug authorization code
      final authCode = credential.authorizationCode;
      debugPrint('Authorization Code present: true');
      debugPrint('Authorization Code length: ${authCode.length}');
      if (authCode.length > 50) {
        debugPrint('Authorization Code (first 50 chars): ${authCode.substring(0, 50)}...');
      } else {
        debugPrint('Authorization Code: $authCode');
      }
      
      debugPrint('=== END APPLE CREDENTIAL DEBUG ===');
    }
  }

  /// Debug Apple Sign-In error with detailed information
  static void debugAppleError(dynamic error, StackTrace? stackTrace) {
    if (kDebugMode) {
      debugPrint('=== APPLE SIGN-IN ERROR DEBUG ===');
      debugPrint('Error: $error');
      debugPrint('Error type: ${error.runtimeType}');
      debugPrint('Error string: ${error.toString()}');
      
      // Check for common Apple Sign-In error codes
      final errorString = error.toString();
      if (errorString.contains('1000')) {
        debugPrint('Error Code 1000: Configuration/Setup Error');
        debugPrint('Possible causes:');
        debugPrint('  - App not configured in Apple Developer Console');
        debugPrint('  - Sign In with Apple capability not enabled');
        debugPrint('  - Running on simulator instead of physical device');
        debugPrint('  - Device not signed in to iCloud');
      } else if (errorString.contains('1001')) {
        debugPrint('Error Code 1001: User Canceled');
        debugPrint('User canceled the Apple Sign-In flow');
      } else if (errorString.contains('1004')) {
        debugPrint('Error Code 1004: Failed');
        debugPrint('Apple Sign-In failed for unknown reason');
      } else {
        debugPrint('Unknown Apple Sign-In error code');
      }
      
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
      
      debugPrint('=== END APPLE ERROR DEBUG ===');
    }
  }

  /// Debug Apple Sign-In availability and environment
  static Future<void> debugAppleEnvironment() async {
    if (kDebugMode) {
      debugPrint('=== APPLE ENVIRONMENT DEBUG ===');
      
      try {
        final isAvailable = await SignInWithApple.isAvailable();
        debugPrint('Apple Sign-In available: $isAvailable');
        
        if (!isAvailable) {
          debugPrint('Apple Sign-In not available. Requirements:');
          debugPrint('  - iOS 13.0 or later');
          debugPrint('  - Physical device (not simulator)');
          debugPrint('  - Device signed in to iCloud');
          debugPrint('  - App configured in Apple Developer Console');
        }
      } catch (e) {
        debugPrint('Error checking Apple Sign-In availability: $e');
      }
      
      debugPrint('Platform: ${defaultTargetPlatform.toString()}');
      debugPrint('Current time: ${DateTime.now().toIso8601String()}');
      debugPrint('=== END APPLE ENVIRONMENT DEBUG ===');
    }
  }

  /// Debug complete Apple Sign-In flow with comprehensive logging
  static Future<AuthorizationCredentialAppleID> debugAppleSignInFlow({
    List<AppleIDAuthorizationScopes> scopes = const [
      AppleIDAuthorizationScopes.email,
      AppleIDAuthorizationScopes.fullName,
    ],
  }) async {
    if (kDebugMode) {
      debugPrint('=== STARTING APPLE SIGN-IN DEBUG FLOW ===');
      await debugAppleEnvironment();
    }

    try {
      debugPrint('Requesting Apple ID credential with scopes: $scopes');
      final credential = await SignInWithApple.getAppleIDCredential(scopes: scopes);
      
      if (kDebugMode) {
        debugPrint('✅ Apple Sign-In credential obtained successfully');
        debugAppleCredential(credential);
      }
      
      return credential;
    } catch (error, stackTrace) {
      if (kDebugMode) {
        debugPrint('❌ Apple Sign-In failed');
        debugAppleError(error, stackTrace);
      }
      rethrow;
    }
  }
}
