import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

/// Helper class for debugging authentication tokens
class TokenDebugHelper {
  /// Debug Google Sign-In tokens
  static void debugGoogleTokens(GoogleSignInAuthentication googleAuth) {
    if (kDebugMode) {
      debugPrint('=== GOOGLE TOKENS DEBUG ===');
      debugPrint('Access Token Present: ${googleAuth.accessToken != null}');
      debugPrint('ID Token Present: ${googleAuth.idToken != null}');
      
      if (googleAuth.accessToken != null) {
        final accessToken = googleAuth.accessToken!;
        debugPrint('Google Access Token: $accessToken');
        debugPrint('Access Token Length: ${accessToken.length}');
      }

      if (googleAuth.idToken != null) {
        final idToken = googleAuth.idToken!;
        debugPrint('Google ID Token: $idToken');
        debugPrint('ID Token Length: ${idToken.length}');
      }
      debugPrint('=== END GOOGLE TOKENS ===');
    }
  }

  /// Debug Firebase ID token
  static Future<void> debugFirebaseToken([User? user]) async {
    if (kDebugMode) {
      final firebaseUser = user ?? FirebaseAuth.instance.currentUser;
      
      if (firebaseUser != null) {
        debugPrint('=== FIREBASE TOKEN DEBUG ===');
        debugPrint('User UID: ${firebaseUser.uid}');
        debugPrint('User Email: ${firebaseUser.email}');
        
        try {
          final token = await firebaseUser.getIdToken();
          if (token != null) {
            debugPrint('Firebase ID Token: $token');
            debugPrint('Token Length: ${token.length}');
          }

          // Get fresh token
          final freshToken = await firebaseUser.getIdToken(true);
          if (freshToken != null) {
            debugPrint('Fresh Firebase Token: $freshToken');
            debugPrint('Fresh Token Length: ${freshToken.length}');
            debugPrint('Tokens Match: ${token == freshToken}');
          }
        } catch (e) {
          debugPrint('Error getting Firebase token: $e');
        }
        debugPrint('=== END FIREBASE TOKEN ===');
      } else {
        debugPrint('No Firebase user found for token debug');
      }
    }
  }

  /// Debug all available tokens
  static Future<void> debugAllTokens() async {
    if (kDebugMode) {
      debugPrint('=== ALL TOKENS DEBUG ===');
      
      // Firebase token
      await debugFirebaseToken();
      
      // Google Sign-In token (if available)
      try {
        final googleSignIn = GoogleSignIn();
        final googleUser = await googleSignIn.signInSilently();
        
        if (googleUser != null) {
          final googleAuth = await googleUser.authentication;
          debugGoogleTokens(googleAuth);
        } else {
          debugPrint('No Google user signed in silently');
        }
      } catch (e) {
        debugPrint('Error getting Google tokens: $e');
      }
      
      debugPrint('=== END ALL TOKENS DEBUG ===');
    }
  }

  /// Decode and debug JWT token payload (basic info only)
  static void debugJWTPayload(String token) {
    if (kDebugMode) {
      try {
        final parts = token.split('.');
        if (parts.length == 3) {
          // Decode the payload (middle part)
          final payload = parts[1];
          // Add padding if needed
          final normalizedPayload = payload.padRight(
            (payload.length + 3) ~/ 4 * 4,
            '=',
          );
          
          debugPrint('=== JWT PAYLOAD DEBUG ===');
          debugPrint('Token Parts: ${parts.length}');
          debugPrint('Header Length: ${parts[0].length}');
          debugPrint('Payload Length: ${parts[1].length}');
          debugPrint('Signature Length: ${parts[2].length}');
          debugPrint('Normalized Payload: $normalizedPayload');
          debugPrint('=== END JWT PAYLOAD ===');
        }
      } catch (e) {
        debugPrint('Error decoding JWT: $e');
      }
    }
  }
}
