/// Utility functions for handling Shopify data formats
class ShopifyUtils {
  /// Extract numeric ID from Shopify GID format
  /// 
  /// Converts: gid://shopify/ProductVariant/46214261801098
  /// To: 46214261801098
  static String extractNumericId(String gid) {
    if (gid.startsWith('gid://shopify/')) {
      final parts = gid.split('/');
      if (parts.length >= 4) {
        return parts.last;
      }
    }
    // If it's already a numeric ID, return as-is
    return gid;
  }

  /// Extract numeric product ID from Shopify GID format
  /// 
  /// Converts: gid://shopify/Product/8538147192970
  /// To: 8538147192970
  static String extractProductId(String gid) {
    return extractNumericId(gid);
  }

  /// Extract numeric variant ID from Shopify GID format
  /// 
  /// Converts: gid://shopify/ProductVariant/46214261801098
  /// To: 46214261801098
  static String extractVariantId(String gid) {
    return extractNumericId(gid);
  }

  /// Check if a string is a Shopify GID format
  static bool isGid(String id) {
    return id.startsWith('gid://shopify/');
  }

  /// Create a Shopify GID from numeric ID and type
  /// 
  /// Example: createGid('46214261801098', 'ProductVariant')
  /// Returns: gid://shopify/ProductVariant/46214261801098
  static String createGid(String numericId, String type) {
    return 'gid://shopify/$type/$numericId';
  }
}
