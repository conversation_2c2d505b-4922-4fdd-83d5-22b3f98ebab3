import '../models/product_model.dart';

/// Utility class for formatting product prices consistently across the app
class PriceFormatter {
  /// Format a price amount with currency code
  static String formatPrice(String amount, String currencyCode) {
    final priceAmount = double.tryParse(amount);
    if (priceAmount == null) return 'Price unavailable';
    
    return '$currencyCode ${priceAmount.toStringAsFixed(2)}';
  }

  /// Extract formatted price from a product
  static String getProductPrice(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        final priceData = variant.price;
        return formatPrice(priceData.amount, priceData.currencyCode);
      }
    } catch (e) {
      // If any error occurs, return default
    }
    return 'Price unavailable';
  }

  /// Extract formatted compare at price from a product
  static String getProductCompareAtPrice(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        if (variant.compareAtPrice != null) {
          final compareAtPriceData = variant.compareAtPrice!;
          return formatPrice(compareAtPriceData.amount, compareAtPriceData.currencyCode);
        }
      }
    } catch (e) {
      // If any error occurs, return empty
    }
    return '';
  }

  /// Get both current price and original price for display
  static PriceInfo getProductPriceInfo(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        final priceData = variant.price;
        final compareAtPriceData = variant.compareAtPrice;

        final currency = priceData.currencyCode;
        final priceAmount = double.tryParse(priceData.amount);

        if (priceAmount != null) {
          final currentPrice = formatPrice(priceData.amount, currency);
          String originalPrice = '';

          // Use compareAtPrice if available, otherwise calculate a fake original price
          if (compareAtPriceData != null) {
            final compareAmount = double.tryParse(compareAtPriceData.amount);
            if (compareAmount != null) {
              originalPrice = formatPrice(compareAtPriceData.amount, currency);
            }
          } else {
            // Create a fake original price that's 20% higher for discount effect
            final fakeOriginalAmount = (priceAmount * 1.2).toStringAsFixed(2);
            originalPrice = formatPrice(fakeOriginalAmount, currency);
          }

          return PriceInfo(
            currentPrice: currentPrice,
            originalPrice: originalPrice,
            hasDiscount: compareAtPriceData != null,
          );
        }
      }
    } catch (e) {
      // If any error occurs, return default
    }
    
    return PriceInfo(
      currentPrice: 'Price unavailable',
      originalPrice: '',
      hasDiscount: false,
    );
  }

  /// Get numeric price value for calculations
  static double getProductPriceValue(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        final priceString = variant.price.amount;
        return double.tryParse(priceString) ?? 0.0;
      }
    } catch (e) {
      // If any error occurs, return 0
    }
    return 0.0;
  }
}

/// Data class to hold price information
class PriceInfo {
  final String currentPrice;
  final String originalPrice;
  final bool hasDiscount;

  const PriceInfo({
    required this.currentPrice,
    required this.originalPrice,
    required this.hasDiscount,
  });
}
