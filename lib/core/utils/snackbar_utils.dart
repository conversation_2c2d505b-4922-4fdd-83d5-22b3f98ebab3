import 'package:flutter/material.dart';

/// Centralized utility class for showing consistent snackbars across the app
class SnackbarUtils {
  // App colors
  static const Color _successColor = Color(0xFF10B981); // Green
  static const Color _errorColor = Color(0xFFEF4444); // Red
  static const Color _warningColor = Color(0xFFF59E0B); // Orange
  static const Color _infoColor = Color(0xFF3B82F6); // Blue
  static const Color _primaryColor = Color(0xFFFF6982); // App primary color

  /// Show a success snackbar with green background
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    _showSnackbar(
      context,
      message: message,
      backgroundColor: _successColor,
      icon: Icons.check_circle_outline,
      duration: duration,
      action: action,
    );
  }

  /// Show an error snackbar with red background
  static void showError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    _showSnackbar(
      context,
      message: message,
      backgroundColor: _errorColor,
      icon: Icons.error_outline,
      duration: duration,
      action: action,
    );
  }

  /// Show a warning snackbar with orange background
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    _showSnackbar(
      context,
      message: message,
      backgroundColor: _warningColor,
      icon: Icons.warning_outlined,
      duration: duration,
      action: action,
    );
  }

  /// Show an info snackbar with blue background
  static void showInfo(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    _showSnackbar(
      context,
      message: message,
      backgroundColor: _infoColor,
      icon: Icons.info_outline,
      duration: duration,
      action: action,
    );
  }

  /// Show a loading snackbar with primary color background
  static void showLoading(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 30),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: _primaryColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.all(16),
      ),
    );
  }

  /// Hide the current snackbar
  static void hide(BuildContext context) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
  }

  /// Private method to show a snackbar with consistent styling
  static void _showSnackbar(
    BuildContext context, {
    required String message,
    required Color backgroundColor,
    required IconData icon,
    required Duration duration,
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.all(16),
        action: action,
      ),
    );
  }

  /// Show authentication success snackbar
  static void showAuthSuccess(
    BuildContext context, {
    String? customMessage,
    bool isFirebaseOnly = false,
  }) {
    final message = customMessage ??
        (isFirebaseOnly
            ? 'Signed in successfully! (Backend temporarily unavailable)'
            : 'Successfully signed in!');

    showSuccess(
      context,
      message,
      duration: Duration(seconds: isFirebaseOnly ? 3 : 2),
    );
  }

  /// Show authentication error snackbar
  static void showAuthError(
    BuildContext context,
    String error, {
    VoidCallback? onRetry,
  }) {
    showError(
      context,
      'Sign-in failed: $error',
      duration: Duration(seconds: 4),
      action: onRetry != null
          ? SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: onRetry,
            )
          : null,
    );
  }

  /// Show cart operation success snackbar
  static void showCartSuccess(
    BuildContext context,
    String message,
  ) {
    showSuccess(
      context,
      message,
      duration: Duration(seconds: 2),
    );
  }

  /// Show cart operation error snackbar
  static void showCartError(
    BuildContext context,
    String error, {
    VoidCallback? onRetry,
  }) {
    showError(
      context,
      error,
      duration: Duration(seconds: 4),
      action: onRetry != null
          ? SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: onRetry,
            )
          : null,
    );
  }

  /// Show Buy Now operation error snackbar
  static void showBuyNowError(
    BuildContext context,
    String error, {
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Buy Now Failed',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 4),
            Text(
              error,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white,
              ),
            ),
          ],
        ),
        backgroundColor: _errorColor,
        duration: Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.all(16),
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// Show Buy Now success snackbar
  static void showBuyNowSuccess(
    BuildContext context,
    String message,
  ) {
    showSuccess(
      context,
      message,
      duration: Duration(seconds: 2),
    );
  }
}
