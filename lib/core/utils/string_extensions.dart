extension StringExtensions on String {
  String get capitalize {
    if (isEmpty) return '';
    return '${this[0].toUpperCase()}${substring(1)}';
  }

  String get titleCase {
    if (isEmpty) return '';
    return split(' ').map((word) => word.capitalize).join(' ');
  }

  String get initials {
    if (isEmpty) return '';

    final List<String> words = trim().split(' ');

    if (words.length <= 1) {
      return isNotEmpty ? substring(0, 1).toUpperCase() : '';
    }

    return words.length > 1
        ? words[0].substring(0, 1).toUpperCase() +
            words[1].substring(0, 1).toUpperCase()
        : words[0].substring(0, 1).toUpperCase();
  }

  String truncate(int maxLength) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}...';
  }

  bool get isValidEmail {
    if (isEmpty) return false;
    final emailRegExp = RegExp(r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+');
    return emailRegExp.hasMatch(this);
  }

  bool get isValidPhone {
    if (isEmpty) return false;
    final phoneRegExp = RegExp(r'^\+?[0-9]{10,15}$');
    return phoneRegExp.hasMatch(this);
  }
}
