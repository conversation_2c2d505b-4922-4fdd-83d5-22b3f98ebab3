import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// API configuration for the backend authentication service
class ApiConfig {
  static const String baseUrl = 'http://192.168.31.199:8889'; // Change this to ngrok URL if needed

  static const String apiVersion = '/api';
  static const String authEndpoint = '/auth/authenticate';

  // Buy Now API endpoints
  static const String buyNowEndpoint = '/buy_now';
  static const String directCheckoutEndpoint = '/direct_checkout';
  
  // Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  /// Create and configure Dio instance for backend API calls
  static Dio createDioClient() {
    final dio = Dio();
    
    // Base options
    dio.options = BaseOptions(
      baseUrl: baseUrl + apiVersion,
      connectTimeout: Duration(milliseconds: connectTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
      sendTimeout: Duration(milliseconds: sendTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add interceptors for debugging in development
    if (kDebugMode) {
      dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: true,
          error: true,
          logPrint: (object) => debugPrint('[API] $object'),
        ),
      );
    }

    // Add error handling interceptor
    dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) {
          debugPrint('[API Error] ${error.message}');
          debugPrint('[API Error] Status Code: ${error.response?.statusCode}');
          debugPrint('[API Error] Response Data: ${error.response?.data}');
          handler.next(error);
        },
      ),
    );

    return dio;
  }

  /// Get full authentication endpoint URL
  static String get authenticationUrl => baseUrl + apiVersion + authEndpoint;

  /// Get full buy now endpoint URL
  static String get buyNowUrl => baseUrl + apiVersion + buyNowEndpoint;

  /// Get full direct checkout endpoint URL
  static String get directCheckoutUrl => baseUrl + apiVersion + directCheckoutEndpoint;

}
