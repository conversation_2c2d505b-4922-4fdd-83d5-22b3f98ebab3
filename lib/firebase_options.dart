// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform, debugPrint;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      debugPrint('Using web Firebase configuration');
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        debugPrint('Using Android Firebase configuration');
        return android;
      case TargetPlatform.iOS:
        debugPrint('Using iOS Firebase configuration');
        return ios;
      case TargetPlatform.macOS:
        debugPrint('Using macOS Firebase configuration');
        return macos;
      case TargetPlatform.windows:
        debugPrint('Using Windows Firebase configuration');
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDoKEHB8yyiFTOuf7EOzXr7hfZ67KBcGcM',
    appId: '1:448909230583:web:48215b8696cf12e2776ef2',
    messagingSenderId: '448909230583',
    projectId: 'travelgator',
    authDomain: 'travelgator.firebaseapp.com',
    storageBucket: 'travelgator.firebasestorage.app',
    measurementId: 'G-3GZHNRHT08',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBHZUrDgPtjcQze-epd-B-JRleD35-VNyk',
    appId: '1:448909230583:android:52be7651a5a0c829776ef2',
    messagingSenderId: '448909230583',
    projectId: 'travelgator',
    storageBucket: 'travelgator.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyALibP6QHJQKgVzKgn16RI7vqTPrQQetVY',
    appId: '1:448909230583:ios:525853fb51744fe5776ef2',
    messagingSenderId: '448909230583',
    projectId: 'travelgator',
    storageBucket: 'travelgator.firebasestorage.app',
    iosClientId: '448909230583-nl54eioo9gr0nr7m24qp3i3om425rvmr.apps.googleusercontent.com',
    iosBundleId: 'com.travelgator.development',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyALibP6QHJQKgVzKgn16RI7vqTPrQQetVY',
    appId: '1:448909230583:ios:0b06b64c620b4410776ef2',
    messagingSenderId: '448909230583',
    projectId: 'travelgator',
    storageBucket: 'travelgator.firebasestorage.app',
    iosClientId: '448909230583-19fh1kr7f4i3irdj9s4nd36t3cccqno9.apps.googleusercontent.com',
    iosBundleId: 'com.example.flutterApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDoKEHB8yyiFTOuf7EOzXr7hfZ67KBcGcM',
    appId: '1:448909230583:web:cab2176351ee014a776ef2',
    messagingSenderId: '448909230583',
    projectId: 'travelgator',
    authDomain: 'travelgator.firebaseapp.com',
    storageBucket: 'travelgator.firebasestorage.app',
    measurementId: 'G-3FHEG1H513',
  );

}