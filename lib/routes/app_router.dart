import 'package:flutter/material.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/features/auth/presentation/screens/temp_auth_screens.dart';
import 'package:flutter_travelgator/features/auth/presentation/screens/auth_modal_test_screen.dart';
import 'package:flutter_travelgator/features/account/account_routes.dart';
import 'package:flutter_travelgator/features/cart/presentation/screens/cart_screen.dart';
import 'package:flutter_travelgator/features/cart/presentation/screens/cart_auth_test_screen.dart';
import 'package:flutter_travelgator/features/home/<USER>/screens/home_screen.dart';
import 'package:flutter_travelgator/features/home/<USER>/screens/sims_screen.dart';
import 'package:flutter_travelgator/features/home/<USER>/screens/travelgator_screen.dart';
import 'package:flutter_travelgator/features/home/<USER>/screens/rewards_screen.dart';

import 'package:flutter_travelgator/features/splash/presentation/screens/splash_screen.dart';
import 'package:flutter_travelgator/features/onboarding/presentation/screens/onboarding_screen.dart';
import 'package:flutter_travelgator/routes/provider/router_state_provider.dart';
import 'package:flutter_travelgator/shared/widgets/bottom_nav_container.dart';

import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_router.g.dart';

/// Routes for the application
class AppRoutes {
  static const splash = '/splash';
  static const onboarding = '/onboarding';
  static const home = '/';
  static const homeTab = '/home';
  static const simsTab = '/sims';
  static const travelgatorTab = '/travelgator';
  static const rewardsTab = '/rewards';
  static const signIn = '/sign-in';
  static const signUp = '/sign-up';
  static const authTest = '/auth-test';
  static const cart = '/cart';



  // Prevent instantiation
  AppRoutes._();
}

/// Application router provider
@riverpod
GoRouter router(Ref ref) {
  // Don't watch auth state to prevent router recreation during auth failures
  // Auth state will be read directly in redirect function when needed
  final routerState = ref.watch(routerStateProvider.notifier);

  debugPrint('[GoRouter] Router provider called - stable router (no auth state watching)');

  return GoRouter(
    initialLocation: AppRoutes.home,
    debugLogDiagnostics: true,
    redirect: (BuildContext context, GoRouterState state) {
      debugPrint('[GoRouter] Redirect called for location: ${state.matchedLocation}');

      // Get the actual auth state for redirect logic
      final actualAuthState = ref.read(authNotifierProvider);

      // Check if the user is authenticated
      final isAuthenticated = actualAuthState.maybeMap(
        authenticated: (_) => true,
        orElse: () => false,
      );

      debugPrint('[GoRouter] Authentication state: $isAuthenticated');

      // Check if auth is in loading state
      final isLoading = actualAuthState.maybeMap(
        loading: (_) => true,
        orElse: () => false,
      );

      // Don't redirect while loading
      if (isLoading) {
        debugPrint('[GoRouter] Auth loading, no redirect');
        return null;
      }

      // Check if this is the first time launching the app
      final isFirstLaunch = routerState.isFirstLaunch();

      // If first launch, show splash screen
      if (isFirstLaunch) {
        debugPrint('[GoRouter] First launch, redirecting to splash');
        return AppRoutes.splash;
      }

      // Handle account routes authentication
      final isAccountRoute = state.matchedLocation.startsWith(
        AccountRoutes.account,
      );

      if (!isAuthenticated && isAccountRoute) {
        debugPrint('[GoRouter] Unauthenticated user on account route, redirecting to sign in');
        return AppRoutes.signIn;
      }

      debugPrint('[GoRouter] No redirect needed');
      return null;
    },
    routes: [
      // Splash screen route
      GoRoute(
        path: AppRoutes.splash,
        builder: (context, state) => const SplashScreen(),
      ),
      // Onboarding screen route
      GoRoute(
        path: AppRoutes.onboarding,
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(path: AppRoutes.home, redirect: (_, __) => AppRoutes.homeTab),
      // Home tab route
      GoRoute(
        path: AppRoutes.homeTab,
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: BottomNavContainer(tabIndex: 0, child: const HomeScreen()),
          );
        },
      ),
      // Sims tab route
      GoRoute(
        path: AppRoutes.simsTab,
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: BottomNavContainer(tabIndex: 1, child: const SimsScreen()),
          );
        },
      ),
      // TravelGator tab route
      GoRoute(
        path: AppRoutes.travelgatorTab,
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: BottomNavContainer(
              tabIndex: 2,
              child: const TravelgatorScreen(),
            ),
          );
        },
      ),
      // Rewards tab route
      GoRoute(
        path: AppRoutes.rewardsTab,
        pageBuilder: (context, state) {
          return NoTransitionPage(
            child: BottomNavContainer(
              tabIndex: 3,
              child: const RewardsScreen(),
            ),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.signIn,
        builder: (context, state) => const SignInScreen(),
      ),
      GoRoute(
        path: AppRoutes.signUp,
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: AppRoutes.authTest,
        builder: (context, state) => const AuthModalTestScreen(),
      ),
      GoRoute(
        path: AppRoutes.cart,
        builder: (context, state) => const CartScreen(),
      ),
      GoRoute(
        path: '/cart-auth-test',
        builder: (context, state) => const CartAuthTestScreen(),
      ),

      // Include all account routes
      ...AccountRoutes.routes,
    ],
    // Handle errors (e.g., page not found)
    errorBuilder:
        (context, state) =>
            Scaffold(body: Center(child: Text('Error: ${state.error}'))),
  );
}
