// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'router_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$routerStateHash() => r'7bf0147c4d4c039f48d9ee3d507b04e82b86c205';

/// Provides app router state management
///
/// Copied from [RouterState].
@ProviderFor(RouterState)
final routerStateProvider =
    AutoDisposeAsyncNotifierProvider<RouterState, void>.internal(
      RouterState.new,
      name: r'routerStateProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$routerStateHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$RouterState = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
