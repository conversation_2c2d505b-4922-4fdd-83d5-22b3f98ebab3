import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'router_state_provider.g.dart';

/// Key for first launch flag in shared preferences
const String kFirstLaunchKey = 'first_launch';

/// Key for onboarding completion flag in shared preferences
const String kOnboardingCompletedKey = 'onboarding_completed';

/// Provides app router state management
@riverpod
class RouterState extends _$RouterState {
  late SharedPreferences _prefs;
  bool _initialized = false;

  @override
  Future<void> build() async {
    _prefs = await SharedPreferences.getInstance();
    _initialized = true;

    // If first launch flag is not set, set it to true (first launch)
    if (!_prefs.containsKey(kFirstLaunchKey)) {
      await _prefs.setBool(kFirstLaunchKey, true);
    }

    // If onboarding completion flag is not set, set it to false (not completed)
    if (!_prefs.containsKey(kOnboardingCompletedKey)) {
      await _prefs.setBool(kOnboardingCompletedKey, false);
    }
  }

  /// Checks if this is the first time launching the app
  bool isFirstLaunch() {
    if (!_initialized) {
      // If not initialized yet, default to true to show splash screen
      return true;
    }

    // Get first launch flag, default to true if not found
    final isFirst = _prefs.getBool(kFirstLaunchKey) ?? true;

    // If it is the first launch, set the flag to false for next time
    if (isFirst) {
      _prefs.setBool(kFirstLaunchKey, false);
    }

    return isFirst;
  }

  /// Checks if onboarding has been completed
  bool isOnboardingCompleted() {
    if (!_initialized) {
      // If not initialized yet, default to false (not completed)
      return false;
    }

    // Get onboarding completion flag, default to false if not found
    return _prefs.getBool(kOnboardingCompletedKey) ?? false;
  }

  /// Mark onboarding as completed
  Future<void> completeOnboarding() async {
    await _prefs.setBool(kOnboardingCompletedKey, true);
  }

  /// Reset first launch state (for testing or settings)
  Future<void> resetFirstLaunch() async {
    await _prefs.setBool(kFirstLaunchKey, true);
  }

  /// Reset onboarding state (for testing or settings)
  Future<void> resetOnboarding() async {
    await _prefs.setBool(kOnboardingCompletedKey, false);
  }

  /// Mark splash screen as completed (for navigation flow)
  Future<void> completeSplash() async {
    // This method can be used to mark splash completion if needed
    // For now, it just ensures the first launch flag is properly set
    if (_initialized && _prefs.getBool(kFirstLaunchKey) == true) {
      await _prefs.setBool(kFirstLaunchKey, false);
    }
  }
}
