import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_travelgator/core/network/api_config.dart';
import 'package:flutter_travelgator/core/utils/shopify_utils.dart';
import '../models/buy_now_response_model.dart';

/// Data source for Buy Now API calls
abstract class BuyNowDataSource {
  Future<BuyNowResponseModel> buyNow({
    required String variantId,
    required int quantity,
    required String backendToken,
    String? productId,
    double? price,
    String? title,
  });

  Future<BuyNowResponseModel> directCheckout({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String backendToken,
  });
}

/// Implementation of Buy Now data source
class BuyNowDataSourceImpl implements BuyNowDataSource {
  final Dio _dio;

  BuyNowDataSourceImpl({Dio? dio}) : _dio = dio ?? ApiConfig.createDioClient();

  @override
  Future<BuyNowResponseModel> buyNow({
    required String variantId,
    required int quantity,
    required String backendToken,
    String? productId,
    double? price,
    String? title,
  }) async {
    try {
      debugPrint('[BuyNowDataSource] Starting buy now request...');
      debugPrint('[BuyNowDataSource] Variant ID: $variantId');
      debugPrint('[BuyNowDataSource] Quantity: $quantity');

      // Validate token format
      if (backendToken.isEmpty) {
        throw Exception('Backend token is empty');
      }

      // Convert Shopify GID to numeric ID if needed
      final numericVariantId = ShopifyUtils.extractNumericId(variantId);
      final numericProductId = productId != null ? ShopifyUtils.extractNumericId(productId) : null;

      final requestData = <String, dynamic>{
        'variant_id': numericVariantId,
        'quantity': quantity,
      };

      // Add optional parameters if provided
      if (numericProductId != null) {
        requestData['product_id'] = numericProductId;
      }
      if (price != null) {
        requestData['price'] = price;
      }
      if (title != null) {
        requestData['title'] = title;
      }

      debugPrint('[BuyNowDataSource] Request data: $requestData');

      final response = await _dio.post(
        ApiConfig.buyNowEndpoint,
        data: requestData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      debugPrint('[BuyNowDataSource] Buy now response: ${response.data}');

      // Validate response structure similar to checkout flow
      if (response.data == null) {
        throw Exception('Empty response from buy now API');
      }

      final responseData = response.data as Map<String, dynamic>;

      // Check if response has the expected structure
      if (!responseData.containsKey('data')) {
        throw Exception('Invalid response format: missing data field');
      }

      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw Exception('Invalid response format: data field is null');
      }

      // Check for success status
      final success = data['success'] as bool?;
      if (success != true) {
        final errorMessage = data['message'] as String? ?? 'Buy now failed';
        throw Exception(errorMessage);
      }

      // Validate checkout URL
      final checkoutUrl = data['checkout_url'] as String?;
      if (checkoutUrl == null || checkoutUrl.isEmpty) {
        throw Exception('No checkout URL received from buy now API');
      }

      return BuyNowResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('[BuyNowDataSource] Dio error: ${e.message}');
      debugPrint('[BuyNowDataSource] Status code: ${e.response?.statusCode}');
      debugPrint('[BuyNowDataSource] Response data: ${e.response?.data}');
      rethrow;
    } catch (e) {
      debugPrint('[BuyNowDataSource] Unexpected error: $e');
      rethrow;
    }
  }

  @override
  Future<BuyNowResponseModel> directCheckout({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String backendToken,
  }) async {
    try {
      debugPrint('[BuyNowDataSource] Starting direct checkout request...');
      debugPrint('[BuyNowDataSource] Variant ID: $variantId');
      debugPrint('[BuyNowDataSource] Product ID: $productId');
      debugPrint('[BuyNowDataSource] Quantity: $quantity');
      debugPrint('[BuyNowDataSource] Price: $price');

      // Validate token format
      if (backendToken.isEmpty) {
        throw Exception('Backend token is empty');
      }

      // Convert Shopify GID to numeric ID if needed
      final numericVariantId = ShopifyUtils.extractNumericId(variantId);
      final numericProductId = ShopifyUtils.extractNumericId(productId);

      final requestData = {
        'variant_id': numericVariantId,
        'product_id': numericProductId,
        'quantity': quantity,
        'price': price,
        'title': title,
      };

      debugPrint('[BuyNowDataSource] Request data: $requestData');

      final response = await _dio.post(
        ApiConfig.directCheckoutEndpoint,
        data: requestData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      debugPrint('[BuyNowDataSource] Direct checkout response: ${response.data}');

      // Validate response structure similar to checkout flow
      if (response.data == null) {
        throw Exception('Empty response from direct checkout API');
      }

      final responseData = response.data as Map<String, dynamic>;

      // Check if response has the expected structure
      if (!responseData.containsKey('data')) {
        throw Exception('Invalid response format: missing data field');
      }

      final data = responseData['data'] as Map<String, dynamic>?;
      if (data == null) {
        throw Exception('Invalid response format: data field is null');
      }

      // Check for success status
      final success = data['success'] as bool?;
      if (success != true) {
        final errorMessage = data['message'] as String? ?? 'Direct checkout failed';
        throw Exception(errorMessage);
      }

      // Validate checkout URL
      final checkoutUrl = data['checkout_url'] as String?;
      if (checkoutUrl == null || checkoutUrl.isEmpty) {
        throw Exception('No checkout URL received from direct checkout API');
      }

      return BuyNowResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('[BuyNowDataSource] Dio error: ${e.message}');
      debugPrint('[BuyNowDataSource] Status code: ${e.response?.statusCode}');
      debugPrint('[BuyNowDataSource] Response data: ${e.response?.data}');
      rethrow;
    } catch (e) {
      debugPrint('[BuyNowDataSource] Unexpected error: $e');
      rethrow;
    }
  }
}
