import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/buy_now_response.dart';

part 'buy_now_response_model.g.dart';

/// Helper function to parse double from string or number
double _parseDouble(dynamic value) {
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.parse(value);
  throw ArgumentError('Cannot parse $value as double');
}

/// Data model for Buy Now API response
@JsonSerializable()
class BuyNowResponseModel {
  final BuyNowDataModel data;

  const BuyNowResponseModel({
    required this.data,
  });

  factory BuyNowResponseModel.fromJson(Map<String, dynamic> json) =>
      _$BuyNowResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$BuyNowResponseModelToJson(this);

  /// Convert to domain entity
  BuyNowResponse toDomain() {
    return BuyNowResponse(
      message: data.success ? 'Buy now successful' : 'Buy now failed',
      checkoutUrl: data.checkoutUrl,
      orderId: data.orderId,
      shopifyCartId: data.shopifyCartId,
      totalPrice: data.totalPrice,
      currency: data.currency,
      status: data.success ? 'success' : 'failed', // Use success status from API response
      orderType: 'buy_now', // Default order type
      item: BuyNowItem(
        variantId: '', // Not provided in this API response
        productId: '', // Not provided in this API response
        quantity: 1, // Default quantity
        price: data.totalPrice,
        title: '', // Not provided in this API response
      ),
    );
  }
}

/// Data model for the nested data object in Buy Now API response
@JsonSerializable()
class BuyNowDataModel {
  final bool success;
  @JsonKey(name: 'checkout_url')
  final String checkoutUrl;
  @JsonKey(name: 'shopify_cart_id')
  final String shopifyCartId;
  @JsonKey(name: 'total_price', fromJson: _parseDouble)
  final double totalPrice;
  final String currency;
  final bool authenticated;
  @JsonKey(name: 'order_id')
  final String orderId;
  @JsonKey(name: 'local_order_created')
  final bool localOrderCreated;

  const BuyNowDataModel({
    required this.success,
    required this.checkoutUrl,
    required this.shopifyCartId,
    required this.totalPrice,
    required this.currency,
    required this.authenticated,
    required this.orderId,
    required this.localOrderCreated,
  });

  factory BuyNowDataModel.fromJson(Map<String, dynamic> json) =>
      _$BuyNowDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$BuyNowDataModelToJson(this);
}


