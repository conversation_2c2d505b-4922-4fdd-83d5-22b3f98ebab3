import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import '../../domain/entities/buy_now_response.dart';
import '../../domain/repositories/buy_now_repository.dart';
import '../datasources/buy_now_data_source.dart';

/// Implementation of Buy Now repository
class BuyNowRepositoryImpl implements BuyNowRepository {
  final BuyNowDataSource _dataSource;
  final Ref _ref;

  BuyNowRepositoryImpl({
    required BuyNowDataSource dataSource,
    required Ref ref,
  })  : _dataSource = dataSource,
        _ref = ref;

  /// Get the current authenticated user from auth state
  EnhancedUserModel? _getCurrentAuthenticatedUser() {
    final authState = _ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          return state.user as EnhancedUserModel;
        }
        return null;
      },
      orElse: () => null,
    );
  }

  @override
  Future<Either<Failure, BuyNowResponse>> buyNow({
    required String variantId,
    required int quantity,
    String? productId,
    double? price,
    String? title,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[BuyNowRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[BuyNowRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      final response = await _dataSource.buyNow(
        variantId: variantId,
        quantity: quantity,
        backendToken: backendToken,
        productId: productId,
        price: price,
        title: title,
      );

      return Right(response.toDomain());
    } on DioException catch (e) {
      debugPrint('[BuyNowRepository] Dio error: ${e.message}');
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      } else if (e.response?.statusCode == 400) {
        final errorMessage = e.response?.data?['error'] ?? 'Bad request';
        return Left(ValidationFailure(message: errorMessage));
      } else if (e.response?.statusCode == 422) {
        final rawError = e.response?.data?['error'] ?? 'Unprocessable entity';
        // Clean up the error message for better user experience
        String userFriendlyError = _cleanErrorMessage(rawError);
        return Left(ValidationFailure(message: userFriendlyError));
      }
      // Handle connection errors and other DioExceptions
      final errorMessage = e.message ?? 'Server error';
      final userFriendlyError = _cleanErrorMessage(errorMessage);
      return Left(ServerFailure(message: userFriendlyError));
    } catch (e) {
      debugPrint('[BuyNowRepository] Unexpected error: $e');
      final userFriendlyError = _cleanErrorMessage(e.toString());
      return Left(UnknownFailure(message: userFriendlyError));
    }
  }

  @override
  Future<Either<Failure, BuyNowResponse>> directCheckout({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[BuyNowRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[BuyNowRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      final response = await _dataSource.directCheckout(
        variantId: variantId,
        productId: productId,
        quantity: quantity,
        price: price,
        title: title,
        backendToken: backendToken,
      );

      return Right(response.toDomain());
    } on DioException catch (e) {
      debugPrint('[BuyNowRepository] Dio error: ${e.message}');
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      } else if (e.response?.statusCode == 400) {
        final errorMessage = e.response?.data?['error'] ?? 'Bad request';
        return Left(ValidationFailure(message: errorMessage));
      } else if (e.response?.statusCode == 422) {
        final rawError = e.response?.data?['error'] ?? 'Unprocessable entity';
        // Clean up the error message for better user experience
        String userFriendlyError = _cleanErrorMessage(rawError);
        return Left(ValidationFailure(message: userFriendlyError));
      }
      // Handle connection errors and other DioExceptions
      final errorMessage = e.message ?? 'Server error';
      final userFriendlyError = _cleanErrorMessage(errorMessage);
      return Left(ServerFailure(message: userFriendlyError));
    } catch (e) {
      debugPrint('[BuyNowRepository] Unexpected error: $e');
      final userFriendlyError = _cleanErrorMessage(e.toString());
      return Left(UnknownFailure(message: userFriendlyError));
    }
  }

  /// Clean up error messages to make them more user-friendly
  String _cleanErrorMessage(String rawError) {
    // Handle connection errors
    if (rawError.contains('Connection refused') || rawError.contains('connection error')) {
      return 'Unable to connect to server. Please check your internet connection and try again.';
    }

    if (rawError.contains('SocketException') || rawError.contains('Network is unreachable')) {
      return 'Network error. Please check your internet connection.';
    }

    if (rawError.contains('timeout') || rawError.contains('Timeout')) {
      return 'Request timed out. Please try again.';
    }

    // Handle common backend error patterns
    if (rawError.contains('undefined method')) {
      return 'Server configuration error. Please try again later.';
    }

    if (rawError.contains('Failed to create buy-now checkout')) {
      return 'Unable to create checkout. Please check your product selection and try again.';
    }

    if (rawError.contains('Failed to create cart')) {
      return 'Unable to add item to cart. Please try again.';
    }

    if (rawError.contains('Authentication') || rawError.contains('Unauthorized')) {
      return 'Please sign in to continue with your purchase.';
    }

    if (rawError.contains('Invalid') || rawError.contains('validation')) {
      return 'Invalid product information. Please try selecting the product again.';
    }

    // If it's a long technical error, provide a generic message
    if (rawError.length > 100) {
      return 'Unable to process your request. Please try again.';
    }

    // Return the original error if it's short and user-friendly
    return rawError;
  }


}
