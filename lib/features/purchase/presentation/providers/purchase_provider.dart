import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/purchase_config.dart';

class PurchaseNotifier extends StateNotifier<PurchaseConfig> {
  PurchaseNotifier(super.initialConfig);

  void updateData(String data) {
    state = state.copyWith(selectedData: data);
  }

  void updateDays(String days) {
    state = state.copyWith(selectedDays: days);
  }

  void updateESimType(ESimType type) {
    debugPrint('🔄 PurchaseNotifier: updateESimType called with ${type.displayName}');
    debugPrint('🔄 PurchaseNotifier: Previous state - eSimType: ${state.eSimType.displayName}, selectedIccid: ${state.selectedIccid}');

    state = state.copyWith(
      eSimType: type,
      // Set default ICCID when switching to top up, clear when switching to new eSIM
      selectedIccid: type == ESimType.topUp
          ? (state.selectedIccid ?? 'Select your TravelGator eSIM')
          : null,
    );

    debugPrint('🔄 PurchaseNotifier: New state - eSimType: ${state.eSimType.displayName}, selectedIccid: ${state.selectedIccid}');
  }

  void updateIccid(String? iccid) {
    debugPrint('🔄 PurchaseNotifier: updateIccid called with: $iccid');
    debugPrint('🔄 PurchaseNotifier: Previous selectedIccid: ${state.selectedIccid}');

    state = state.copyWith(selectedIccid: iccid);

    debugPrint('🔄 PurchaseNotifier: New selectedIccid: ${state.selectedIccid}');
  }

  void updateQuantity(int quantity) {
    if (quantity > 0) {
      debugPrint('🔢 PurchaseNotifier: updateQuantity called with: $quantity');
      debugPrint('🔢 PurchaseNotifier: Previous quantity: ${state.quantity}, unitPrice: ${state.unitPrice}, totalPrice: ${state.totalPrice}');

      state = state.copyWith(quantity: quantity);

      debugPrint('🔢 PurchaseNotifier: New quantity: ${state.quantity}, totalPrice: ${state.totalPrice}');
    }
  }

  void incrementQuantity() {
    final newQuantity = state.quantity + 1;
    debugPrint('🔢 PurchaseNotifier: incrementQuantity from ${state.quantity} to $newQuantity');
    debugPrint('🔢 PurchaseNotifier: Previous totalPrice: ${state.totalPrice}');

    state = state.copyWith(quantity: newQuantity);

    debugPrint('🔢 PurchaseNotifier: New totalPrice: ${state.totalPrice}');
  }

  void decrementQuantity() {
    if (state.quantity > 1) {
      final newQuantity = state.quantity - 1;
      debugPrint('🔢 PurchaseNotifier: decrementQuantity from ${state.quantity} to $newQuantity');
      debugPrint('🔢 PurchaseNotifier: Previous totalPrice: ${state.totalPrice}');

      state = state.copyWith(quantity: newQuantity);

      debugPrint('🔢 PurchaseNotifier: New totalPrice: ${state.totalPrice}');
    } else {
      debugPrint('🔢 PurchaseNotifier: Cannot decrement quantity below 1');
    }
  }

  void toggleDetails() {
    debugPrint('🔄 toggleDetails called - current: ${state.showDetails}');
    state = state.copyWith(showDetails: !state.showDetails);
    debugPrint('🔄 toggleDetails completed - new: ${state.showDetails}');
  }
}

// Provider for purchase configuration
final purchaseProvider = StateNotifierProvider.family<PurchaseNotifier, PurchaseConfig, PurchaseConfig>(
  (ref, initialConfig) => PurchaseNotifier(initialConfig),
);

// Data options provider
final dataOptionsProvider = Provider<List<String>>((ref) {
  return ['1GB', '2GB', '3GB', '5GB', '10GB', '12GB'];
});

// Days options provider
final daysOptionsProvider = Provider<List<String>>((ref) {
  return ['1 day', '3 days', '7 days', '15 days', '30 days'];
});

// Mock ICCID options provider (in real app, this would come from API)
final iccidOptionsProvider = Provider<List<String>>((ref) {
  return [
    'Select your TravelGator eSIM',
    '8901234567890123456',
    '8901234567890123457',
    '8901234567890123458',
  ];
});
