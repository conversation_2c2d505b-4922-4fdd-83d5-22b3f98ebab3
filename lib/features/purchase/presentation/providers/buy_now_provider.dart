import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/network/api_config.dart';
import '../../data/datasources/buy_now_data_source.dart';
import '../../data/repositories/buy_now_repository_impl.dart';
import '../../domain/entities/buy_now_response.dart';
import '../../domain/repositories/buy_now_repository.dart';
import '../../domain/usecases/buy_now_use_case.dart';

/// Provider for Buy Now data source
final buyNowDataSourceProvider = Provider<BuyNowDataSource>((ref) {
  return BuyNowDataSourceImpl(dio: ApiConfig.createDioClient());
});

/// Provider for Buy Now repository
final buyNowRepositoryProvider = Provider<BuyNowRepository?>((ref) {
  try {
    return BuyNowRepositoryImpl(
      dataSource: ref.watch(buyNowDataSourceProvider),
      ref: ref,
    );
  } catch (e) {
    debugPrint('[BuyNowProvider] Error creating repository: $e');
    return null;
  }
});

/// Provider for Buy Now use case
final buyNowUseCaseProvider = Provider<BuyNowUseCase?>((ref) {
  final repository = ref.watch(buyNowRepositoryProvider);
  if (repository == null) return null;
  return BuyNowUseCase(repository);
});

/// Provider for Direct Checkout use case
final directCheckoutUseCaseProvider = Provider<DirectCheckoutUseCase?>((ref) {
  final repository = ref.watch(buyNowRepositoryProvider);
  if (repository == null) return null;
  return DirectCheckoutUseCase(repository);
});

/// State for Buy Now operations
enum BuyNowStatus { idle, loading, success, error }

class BuyNowState {
  final BuyNowStatus status;
  final BuyNowResponse? response;
  final String? errorMessage;

  const BuyNowState({
    this.status = BuyNowStatus.idle,
    this.response,
    this.errorMessage,
  });

  BuyNowState copyWith({
    BuyNowStatus? status,
    BuyNowResponse? response,
    String? errorMessage,
  }) {
    return BuyNowState(
      status: status ?? this.status,
      response: response ?? this.response,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() {
    return 'BuyNowState(status: $status, response: $response, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BuyNowState &&
        other.status == status &&
        other.response == response &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode => status.hashCode ^ response.hashCode ^ errorMessage.hashCode;
}

/// Notifier for Buy Now state management
class BuyNowNotifier extends StateNotifier<BuyNowState> {
  final BuyNowUseCase? _buyNowUseCase;
  final DirectCheckoutUseCase? _directCheckoutUseCase;

  BuyNowNotifier(this._buyNowUseCase, this._directCheckoutUseCase) : super(const BuyNowState());

  /// Execute buy now with simplified parameters
  Future<void> buyNow({
    required String variantId,
    required int quantity,
    String? productId,
    double? price,
    String? title,
  }) async {
    if (_buyNowUseCase == null) {
      state = state.copyWith(
        status: BuyNowStatus.error,
        errorMessage: 'Buy Now service not available',
      );
      return;
    }

    state = state.copyWith(status: BuyNowStatus.loading);

    final result = await _buyNowUseCase.call(
      variantId: variantId,
      quantity: quantity,
      productId: productId,
      price: price,
      title: title,
    );

    result.fold(
      (failure) {
        debugPrint('[BuyNowNotifier] Buy now failed: ${failure.message}');
        state = state.copyWith(
          status: BuyNowStatus.error,
          errorMessage: failure.message,
          response: null, // Clear any previous response
        );
      },
      (response) {
        debugPrint('[BuyNowNotifier] Buy now successful: ${response.checkoutUrl}');
        debugPrint('[BuyNowNotifier] Order ID: ${response.orderId}');
        debugPrint('[BuyNowNotifier] Total Price: ${response.totalPrice} ${response.currency}');
        state = state.copyWith(
          status: BuyNowStatus.success,
          response: response,
          errorMessage: null, // Clear any previous error
        );
      },
    );
  }

  /// Execute direct checkout with full validation
  Future<void> directCheckout({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
  }) async {
    if (_directCheckoutUseCase == null) {
      state = state.copyWith(
        status: BuyNowStatus.error,
        errorMessage: 'Direct Checkout service not available',
      );
      return;
    }

    state = state.copyWith(status: BuyNowStatus.loading);

    final result = await _directCheckoutUseCase.call(
      variantId: variantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
    );

    result.fold(
      (failure) {
        debugPrint('[BuyNowNotifier] Direct checkout failed: ${failure.message}');
        state = state.copyWith(
          status: BuyNowStatus.error,
          errorMessage: failure.message,
          response: null, // Clear any previous response
        );
      },
      (response) {
        debugPrint('[BuyNowNotifier] Direct checkout successful: ${response.checkoutUrl}');
        debugPrint('[BuyNowNotifier] Order ID: ${response.orderId}');
        debugPrint('[BuyNowNotifier] Total Price: ${response.totalPrice} ${response.currency}');
        state = state.copyWith(
          status: BuyNowStatus.success,
          response: response,
          errorMessage: null, // Clear any previous error
        );
      },
    );
  }

  /// Reset state to idle
  void reset() {
    state = const BuyNowState();
  }
}

/// Provider for Buy Now state notifier
final buyNowProvider = StateNotifierProvider<BuyNowNotifier, BuyNowState>((ref) {
  final buyNowUseCase = ref.watch(buyNowUseCaseProvider);
  final directCheckoutUseCase = ref.watch(directCheckoutUseCaseProvider);
  
  return BuyNowNotifier(buyNowUseCase, directCheckoutUseCase);
});
