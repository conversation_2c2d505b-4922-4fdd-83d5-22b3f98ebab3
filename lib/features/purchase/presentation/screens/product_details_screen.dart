import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/product_model.dart';
import '../../../cart/presentation/providers/cart_provider.dart';
import '../../../cart/presentation/providers/cart_state.dart';
import '../widgets/purchase_actions.dart';

class ProductDetailsScreen extends ConsumerWidget {
  final Product product;
  final VoidCallback onClose;

  const ProductDetailsScreen({
    super.key,
    required this.product,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            height: 48,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFE5E7EB),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Empty space for symmetry
                const SizedBox(width: 56),
                // Title
                Expanded(
                  child: Text(
                    'Details',
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Color(0xFF1F2937),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                // Close button
                GestureDetector(
                  onTap: onClose,
                  child: const SizedBox(
                    width: 56,
                    height: 48,
                    child: Icon(
                      Icons.close,
                      color: Color(0xFF1F2937),
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Content
          Expanded(
            child: Column(
              children: [
                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product title
                        Text(
                          product.title,
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w500,
                            fontSize: 18,
                            color: Color(0xFF1F2937),
                            height: 1.21,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Product image placeholder
                        Container(
                          width: double.infinity,
                          height: 451,
                          decoration: BoxDecoration(
                            color: const Color(0xFFF3F4F6),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: const Color(0xFFE5E7EB),
                              width: 1,
                            ),
                          ),
                          child: product.featuredImage != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    product.featuredImage!.url,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Center(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.image_outlined,
                                              size: 48,
                                              color: Color(0xFF9CA3AF),
                                            ),
                                            SizedBox(height: 8),
                                            Text(
                                              'Product Image',
                                              style: TextStyle(
                                                color: Color(0xFF9CA3AF),
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                )
                              : const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.image_outlined,
                                        size: 48,
                                        color: Color(0xFF9CA3AF),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Product Image',
                                        style: TextStyle(
                                          color: Color(0xFF9CA3AF),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                        ),
                        const SizedBox(height: 16),
                        // Hide details button
                        GestureDetector(
                          onTap: onClose,
                          child: const Text(
                            'Hide details',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              color: Color(0xFFFF6982),
                              letterSpacing: 0.0125,
                              height: 1.21,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Action buttons
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
                  child: PurchaseActions(
                    onAddToCart: () => _handleAddToCart(context, ref),
                    variantId: product.variants.edges.isNotEmpty ? product.variants.edges.first.node.id : null,
                    productId: product.id,
                    quantity: 1,
                    price: product.variants.edges.isNotEmpty ? double.tryParse(product.variants.edges.first.node.price.amount) : null,
                    title: product.title,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleAddToCart(BuildContext context, WidgetRef ref) async {
    // Get the first variant (for simplicity, in a real app you'd let user choose)
    if (product.variants.edges.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No variants available for this product')),
      );
      return;
    }

    final variant = product.variants.edges.first.node;
    final price = double.tryParse(variant.price.amount) ?? 0.0;

    // Get the cart state before the operation
    final cartStateBefore = ref.read(cartProvider);
    debugPrint('[ProductDetails] Cart state before add: ${cartStateBefore.status}, items: ${cartStateBefore.cart?.itemsCount ?? 0}');

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      await ref.read(cartProvider.notifier).addItem(
        variantId: variant.id,
        productId: product.id,
        quantity: 1,
        price: price,
        title: product.title,
        currency: variant.price.currencyCode,
        imageUrl: product.featuredImage?.url,
      );

      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Wait a bit longer for state to update
      await Future.delayed(const Duration(milliseconds: 200));

      // Check the cart state after the operation to determine success/failure
      final cartStateAfter = ref.read(cartProvider);
      debugPrint('[ProductDetails] Cart state after add: ${cartStateAfter.status}, items: ${cartStateAfter.cart?.itemsCount ?? 0}, error: ${cartStateAfter.error}');

      if (context.mounted) {
        if (cartStateAfter.status == CartStatus.error) {
          // Operation failed - show error message (ProductDetails doesn't need to close modal)
          debugPrint('[ProductDetails] ❌ Showing error SnackBar: ${cartStateAfter.error}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to add to cart: ${cartStateAfter.error ?? 'Unknown error'}'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 4),
            ),
          );
        } else if (cartStateAfter.status == CartStatus.loaded &&
                   cartStateAfter.cart != null &&
                   (cartStateBefore.cart?.itemsCount ?? 0) < cartStateAfter.cart!.itemsCount) {
          // Operation succeeded - item count increased
          debugPrint('[ProductDetails] ✅ Showing success SnackBar: item count increased from ${cartStateBefore.cart?.itemsCount ?? 0} to ${cartStateAfter.cart!.itemsCount}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${product.title} added to cart!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          // Unclear state - show generic message based on what we know
          debugPrint('[ProductDetails] ⚠️ Unclear cart state: ${cartStateAfter.status}');
          if (cartStateAfter.status == CartStatus.loading) {
            // Still loading - this shouldn't happen but handle it
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Cart operation is still processing...'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 2),
              ),
            );
          } else {
            // Unknown state - assume error for safety
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Cart operation may have failed. Please check your cart.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('[ProductDetails] Exception during add to cart: $e');
      // Hide loading indicator if still showing
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add to cart: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 4),
          ),
        );
      }
    }
  }


}
