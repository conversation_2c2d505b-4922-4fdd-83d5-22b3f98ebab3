import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:go_router/go_router.dart';

/// Screen for handling Shopify checkout in WebView
class CheckoutWebViewScreen extends StatefulWidget {
  final String checkoutUrl;
  final String orderId;

  const CheckoutWebViewScreen({
    super.key,
    required this.checkoutUrl,
    required this.orderId,
  });

  @override
  State<CheckoutWebViewScreen> createState() => _CheckoutWebViewScreenState();
}

class _CheckoutWebViewScreenState extends State<CheckoutWebViewScreen> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    debugPrint('[CheckoutWebViewScreen] Initializing with URL: ${widget.checkoutUrl}');
    debugPrint('[CheckoutWebViewScreen] Order ID: ${widget.orderId}');
    _initializeWebView();

    // Add a timer to check if the screen stays visible
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        debugPrint('[CheckoutWebViewScreen] Screen still mounted after 2 seconds');
        debugPrint('[CheckoutWebViewScreen] Current loading state: $_isLoading');
      } else {
        debugPrint('[CheckoutWebViewScreen] Screen was disposed within 2 seconds!');
      }
    });
  }

  @override
  void dispose() {
    debugPrint('[CheckoutWebViewScreen] Screen is being disposed');
    super.dispose();
  }

  void _initializeWebView() {
    debugPrint('[CheckoutWebViewScreen] Setting up WebView controller');

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            debugPrint('[CheckoutWebViewScreen] Page started loading: $url');
            setState(() {
              _isLoading = true;
            });
            _handleNavigation(url);
          },
          onPageFinished: (String url) {
            debugPrint('[CheckoutWebViewScreen] Page finished loading: $url');
            debugPrint('[CheckoutWebViewScreen] Setting isLoading to false');
            setState(() {
              _isLoading = false;
            });
            debugPrint('[CheckoutWebViewScreen] UI should now show WebView content');
            // Also check completion on page finish (in case redirect happens quickly)
            _handleNavigation(url);
          },
          onNavigationRequest: (NavigationRequest request) {
            debugPrint('[CheckoutWebViewScreen] Navigation request to: ${request.url}');
            _handleNavigation(request.url);
            return NavigationDecision.navigate;
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('[CheckoutWebViewScreen] WebView error: ${error.description}');
            debugPrint('[CheckoutWebViewScreen] Error code: ${error.errorCode}');
            debugPrint('[CheckoutWebViewScreen] Error type: ${error.errorType}');
          },
        ),
      );

    debugPrint('[CheckoutWebViewScreen] Loading checkout URL: ${widget.checkoutUrl}');
    _controller.loadRequest(Uri.parse(widget.checkoutUrl));
  }

  void _handleNavigation(String url) {
    debugPrint('[CheckoutWebViewScreen] Handling navigation to: $url');

    // Check for checkout completion patterns
    if (_isCheckoutComplete(url)) {
      debugPrint('[CheckoutWebViewScreen] Checkout completed, redirecting to home');
      _onCheckoutComplete();
    } else {
      debugPrint('[CheckoutWebViewScreen] URL does not match completion patterns, continuing...');
    }
  }

  bool _isCheckoutComplete(String url) {
    // Traditional thank you page pattern
    if (url.contains('/checkout/thank_you') || url.contains('/thank_you')) {
      return true;
    }

    // Modern Shopify checkout completion patterns
    if (url.contains('/checkouts/') && url.contains('/processing')) {
      return true;
    }

    // Custom deep link pattern (if configured in Shopify)
    if (url.startsWith('myapp://checkout/success')) {
      return true;
    }

    // Additional patterns for 2025 Shopify checkout
    if (url.contains('checkout.pci.shopifyinc.com') && url.contains('success')) {
      return true;
    }

    // Shopify invoice completion patterns
    if (url.contains('/invoices/') && (url.contains('/paid') || url.contains('/complete') || url.contains('/success'))) {
      return true;
    }

    // Payment completion redirects
    if (url.contains('payment_status=paid') || url.contains('status=completed') || url.contains('payment=success')) {
      return true;
    }

    // Shopify draft order completion
    if (url.contains('draft_order') && (url.contains('complete') || url.contains('paid'))) {
      return true;
    }

    // Order status pages
    if (url.contains('/orders/') && url.contains('status')) {
      return true;
    }

    // Checkout completion indicators
    if (url.contains('checkout_complete=true') || url.contains('order_id=') || url.contains('order_number=')) {
      return true;
    }

    // Modern Shopify cart checkout completion patterns (2025)
    // Pattern: /checkouts/cn/[token] followed by completion
    if (url.contains('/checkouts/cn/') && (url.contains('/complete') || url.contains('/success') || url.contains('/thank_you'))) {
      return true;
    }

    // Shopify checkout completion via redirect to home
    final uri = Uri.tryParse(url);
    if (uri != null && uri.host.contains('myshopify.com')) {
      // Check if redirected to store home page (common completion pattern)
      if (uri.path == '/' || uri.path.isEmpty) {
        return true;
      }

      // Check for order confirmation pages
      if (uri.path.contains('/orders/') || uri.path.contains('/account/orders/')) {
        return true;
      }
    }

    debugPrint('[CheckoutWebViewScreen] URL does not match completion patterns: $url');
    return false;
  }

  void _onCheckoutComplete() {
    debugPrint('[CheckoutWebViewScreen] Checkout completion detected!');
    debugPrint('[CheckoutWebViewScreen] Order ID: ${widget.orderId}');

    // Show success message
    if (mounted) {
      // Clear any existing SnackBars before showing success message
      ScaffoldMessenger.of(context).clearSnackBars();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🎉 Order completed successfully!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );

      debugPrint('[CheckoutWebViewScreen] Success message shown, navigating to home');

      // Navigate to home screen and clear all SnackBars
      final messenger = ScaffoldMessenger.of(context);
      final router = GoRouter.of(context);
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          debugPrint('[CheckoutWebViewScreen] Navigating to home screen');
          messenger.clearSnackBars();
          router.go('/');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[CheckoutWebViewScreen] Building widget - isLoading: $_isLoading');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            debugPrint('[CheckoutWebViewScreen] Close button pressed');
            Navigator.of(context).pop();
          },
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              ),
            ),
        ],
      ),
      body: Container(
        color: Colors.white, // Ensure background is visible
        child: Stack(
          children: [
            // WebView
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red, width: 2), // Debug border
              ),
              child: WebViewWidget(controller: _controller),
            ),

            // Loading overlay
            if (_isLoading)
              Container(
                color: Colors.white.withValues(alpha: 0.8),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Loading checkout...'),
                    ],
                  ),
                ),
              ),

            // Debug overlay in debug mode
            if (kDebugMode)
              Positioned(
                top: 10,
                left: 10,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'WebView Debug\nLoading: $_isLoading',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
