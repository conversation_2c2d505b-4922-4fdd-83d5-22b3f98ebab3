import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/models/product_model.dart';
import '../../../../core/utils/price_formatter.dart';
import '../../../cart/presentation/providers/cart_provider.dart';
import '../../../cart/presentation/providers/cart_state.dart';
import '../../domain/models/purchase_config.dart';
import '../providers/purchase_provider.dart';
import '../widgets/purchase_header.dart';
import '../widgets/selection_dropdown.dart';
import '../widgets/esim_type_selector.dart';
import '../widgets/quantity_selector.dart';
import '../widgets/purchase_actions.dart';

class PrePurchaseScreen extends ConsumerStatefulWidget {
  final Product product;

  const PrePurchaseScreen({
    super.key,
    required this.product,
  });

  @override
  ConsumerState<PrePurchaseScreen> createState() => _PrePurchaseScreenState();
}

class _PrePurchaseScreenState extends ConsumerState<PrePurchaseScreen> {
  bool _showDetails = false;
  bool _isHandlingCartOperation = false; // Flag to prevent duplicate operations
  DateTime? _lastAddToCartClick;
  static const Duration _debounceDelay = Duration(seconds: 2);

  @override
  Widget build(BuildContext context) {
    final productPrice = _getProductPrice(widget.product);
    final productCurrency = _getProductCurrency(widget.product);

    final initialConfig = PurchaseConfig(
      productId: widget.product.id,
      productTitle: widget.product.title,
      productImageUrl: widget.product.featuredImage?.url,
      unitPrice: productPrice,
    );

    return Consumer(
      builder: (context, ref, child) {
        final purchaseStateProvider = purchaseProvider(initialConfig);
        final purchaseState = ref.watch(purchaseStateProvider);
        final purchaseNotifier = ref.read(purchaseStateProvider.notifier);
        final dataOptions = ref.watch(dataOptionsProvider);
        final daysOptions = ref.watch(daysOptionsProvider);
        final iccidOptions = ref.watch(iccidOptionsProvider);

        debugPrint('🎨 UI Rebuild - Quantity: ${purchaseState.quantity}, Total: ${purchaseState.totalPrice}');

        return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
            minHeight: MediaQuery.of(context).size.height * 0.3,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: IntrinsicHeight(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PurchaseHeader(showDetails: _showDetails),
                // Bottom section (matching Figma structure)
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Content section (white background)
                      Flexible(
                        child: SingleChildScrollView(
                          child: Container(
                            color: Colors.white,
                            padding: const EdgeInsets.fromLTRB(20, 20, 20, 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Product title (matching Figma exactly)
                                Text(
                                  purchaseState.productTitle,
                                  style: GoogleFonts.inter(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2937),
                                    height: 1.21,
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // Show details view or form based on state
                                if (_showDetails) ...[
                                  _buildDetailsView(),
                                ] else ...[
                                  _buildPurchaseForm(purchaseState, purchaseNotifier, dataOptions, daysOptions, iccidOptions),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Show quantity/price and buttons only when not in details view
                      if (!_showDetails) ...[
                        // Gray section for quantity and price (matching Figma Frame 5)
                        Container(
                          color: const Color(0xFFF9FAFB),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Quantity selector with fixed width
                              SizedBox(
                                width: 120,
                                child: QuantitySelector(
                                  quantity: purchaseState.quantity,
                                  onIncrement: () {
                                    debugPrint('🔢 Incrementing quantity from ${purchaseState.quantity} to ${purchaseState.quantity + 1}');
                                    purchaseNotifier.incrementQuantity();
                                  },
                                  onDecrement: () {
                                    debugPrint('🔢 Decrementing quantity from ${purchaseState.quantity} to ${purchaseState.quantity - 1}');
                                    purchaseNotifier.decrementQuantity();
                                  },
                                ),
                              ),
                              // Flexible total payment section
                              Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(
                                      'Total payment:',
                                      style: GoogleFonts.inter(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2937),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: FittedBox(
                                        fit: BoxFit.scaleDown,
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                          _formatPrice(purchaseState.totalPrice, productCurrency),
                                          style: GoogleFonts.inter(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                            color: const Color(0xFF111827),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Button section (matching Figma Button frame)
                        Container(
                          color: Colors.white,
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
                          child: PurchaseActions(
                            onAddToCart: () => _handleAddToCart(context, ref, purchaseState),
                            variantId: widget.product.variants.edges.isNotEmpty ? widget.product.variants.edges.first.node.id : null,
                            productId: widget.product.id,
                            quantity: purchaseState.quantity,
                            price: _getProductPrice(widget.product),
                            title: '${widget.product.title} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})',
                            onCloseModal: () => Navigator.of(context).pop(),
                          ),
                        ),
                      ] else ...[
                        // Button section for details view
                        Container(
                          color: Colors.white,
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
                          child: PurchaseActions(
                            onAddToCart: () => _handleAddToCart(context, ref, purchaseState),
                            variantId: widget.product.variants.edges.isNotEmpty ? widget.product.variants.edges.first.node.id : null,
                            productId: widget.product.id,
                            quantity: purchaseState.quantity,
                            price: _getProductPrice(widget.product),
                            title: '${widget.product.title} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})',
                            onCloseModal: () => Navigator.of(context).pop(),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
      },
    );
  }



  Widget _buildPurchaseForm(
    PurchaseConfig purchaseState,
    PurchaseNotifier purchaseNotifier,
    List<String> dataOptions,
    List<String> daysOptions,
    List<String> iccidOptions,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // See details button (positioned after product title)
        Align(
          alignment: Alignment.center,
          child: TextButton(
            onPressed: () {
              setState(() {
                _showDetails = true;
              });
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              'See details',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: const Color(0xFFFF6982),
                decoration: TextDecoration.underline,
                decorationColor: const Color(0xFFFF6982),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Selection dropdowns row
        Row(
          children: [
            Expanded(
              child: SelectionDropdown(
                label: 'Your data',
                value: purchaseState.selectedData,
                options: dataOptions,
                onChanged: (value) {
                  if (value != null) {
                    purchaseNotifier.updateData(value);
                  }
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: SelectionDropdown(
                label: 'Day',
                value: purchaseState.selectedDays,
                options: daysOptions,
                onChanged: (value) {
                  if (value != null) {
                    purchaseNotifier.updateDays(value);
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // eSIM type selector
        ESimTypeSelector(
          selectedType: purchaseState.eSimType,
          onChanged: (type) {
            debugPrint('🔄 eSIM type changed to: ${type.displayName}');
            purchaseNotifier.updateESimType(type);
          },
        ),

        // Conditional ICCID dropdown
        if (purchaseState.eSimType == ESimType.topUp) ...[
          const SizedBox(height: 16),
          SelectionDropdown(
            label: 'ICCID for Top Up',
            value: purchaseState.selectedIccid ?? 'Select your TravelGator eSIM',
            options: iccidOptions,
            onChanged: (value) {
              debugPrint('🔄 ICCID changed to: $value');
              purchaseNotifier.updateIccid(value);
            },
          ),
          // const SizedBox(height: 8), // Add spacing before gray section
        ],
      ],
    );
  }

  Widget _buildDetailsView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product image placeholder (matching Figma design)
        Container(
          width: double.infinity,
          height: 300,
          decoration: BoxDecoration(
            color: const Color(0xFFF3F4F6),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE5E7EB)),
          ),
          child: const Center(
            child: Icon(
              Icons.image,
              size: 64,
              color: Color(0xFF9CA3AF),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Hide details button
        Align(
          alignment: Alignment.centerLeft,
          child: TextButton(
            onPressed: () {
              setState(() {
                _showDetails = false;
              });
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              'Hide details',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFFFF6982),
                decoration: TextDecoration.underline,
                decorationColor: const Color(0xFFFF6982),
              ),
            ),
          ),
        ),
      ],
    );
  }

  double _getProductPrice(Product product) {
    return PriceFormatter.getProductPriceValue(product);
  }

  String _getProductCurrency(Product product) {
    try {
      if (product.variants.edges.isNotEmpty) {
        final variant = product.variants.edges.first.node;
        return variant.price.currencyCode;
      }
    } catch (e) {
      debugPrint('Error getting product currency: $e');
    }
    return 'USD'; // Default fallback
  }

  String _formatPrice(double amount, String currency) {
    return PriceFormatter.formatPrice(amount.toStringAsFixed(2), currency);
  }

  /// Handle add to cart functionality with proper cart integration
  void _handleAddToCart(BuildContext context, WidgetRef ref, PurchaseConfig purchaseState) async {
    // Prevent duplicate operations
    if (_isHandlingCartOperation) {
      debugPrint('[PrePurchaseScreen] Cart operation already in progress, ignoring duplicate call');
      return;
    }

    // Prevent spam clicking with time-based debounce
    final now = DateTime.now();
    if (_lastAddToCartClick != null &&
        now.difference(_lastAddToCartClick!) < _debounceDelay) {
      debugPrint('[PrePurchaseScreen] Add to Cart clicked too soon, ignoring (debounce)');
      return;
    }
    _lastAddToCartClick = now;

    // Get the first variant (for simplicity, in a real app you'd let user choose)
    if (widget.product.variants.edges.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No variants available for this product')),
      );
      return;
    }

    final variant = widget.product.variants.edges.first.node;
    final unitPrice = double.tryParse(variant.price.amount) ?? 0.0;
    final totalPrice = unitPrice * purchaseState.quantity;

    debugPrint('🛒 Adding to cart:');
    debugPrint('🛒 Unit price: $unitPrice ${variant.price.currencyCode}');
    debugPrint('🛒 Quantity: ${purchaseState.quantity}');
    debugPrint('🛒 Total price: $totalPrice ${variant.price.currencyCode}');
    debugPrint('🛒 Purchase state total: ${purchaseState.totalPrice}');

    // Set flag to prevent duplicate operations
    _isHandlingCartOperation = true;

    // Get the cart state before the operation
    final cartStateBefore = ref.read(cartProvider);
    debugPrint('[PrePurchaseScreen] Cart state before add: ${cartStateBefore.status}, items: ${cartStateBefore.cart?.itemsCount ?? 0}');

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      await ref.read(cartProvider.notifier).addItem(
        variantId: variant.id,
        productId: widget.product.id,
        quantity: purchaseState.quantity,
        price: unitPrice, // Send unit price, not total price
        title: '${widget.product.title} - ${purchaseState.selectedData} for ${purchaseState.selectedDays} (${purchaseState.eSimType.displayName})',
        currency: variant.price.currencyCode,
        imageUrl: widget.product.featuredImage?.url,
      );

      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Wait for state to update
      await Future.delayed(const Duration(milliseconds: 200));

      // Check the cart state after the operation to determine success/failure
      final cartStateAfter = ref.read(cartProvider);
      debugPrint('[PrePurchaseScreen] Cart state after add: ${cartStateAfter.status}, items: ${cartStateAfter.cart?.itemsCount ?? 0}, error: ${cartStateAfter.error}');

      if (context.mounted) {
        if (cartStateAfter.status == CartStatus.error) {
          // Operation failed - close modal first, then show error message
          debugPrint('[PrePurchaseScreen] ❌ Showing error SnackBar: ${cartStateAfter.error}');

          // Capture context before closing modal
          final rootContext = Navigator.of(context, rootNavigator: true).context;
          final errorMessage = cartStateAfter.error ?? 'Unknown error';

          try {
            // Close the modal first
            Navigator.of(context).pop();
            debugPrint('[PrePurchaseScreen] Modal closed due to error');
          } catch (navError) {
            debugPrint('[PrePurchaseScreen] Could not close modal: $navError');
          }

          // Show error immediately in parent context
          try {
            ScaffoldMessenger.of(rootContext).showSnackBar(
              SnackBar(
                content: Text('Failed to add to cart: $errorMessage'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 4),
              ),
            );
            debugPrint('[PrePurchaseScreen] Error SnackBar shown');
          } catch (snackError) {
            debugPrint('[PrePurchaseScreen] Could not show error SnackBar: $snackError');
          }

          // Reset flag
          _isHandlingCartOperation = false;
        } else if (cartStateAfter.status == CartStatus.loaded &&
                   cartStateAfter.cart != null &&
                   (cartStateBefore.cart?.itemsCount ?? 0) < cartStateAfter.cart!.itemsCount) {
          // Operation succeeded - item count increased
          debugPrint('[PrePurchaseScreen] ✅ Showing success SnackBar: item count increased from ${cartStateBefore.cart?.itemsCount ?? 0} to ${cartStateAfter.cart!.itemsCount}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${widget.product.title} added to cart!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
          // Close the modal after successful add to cart
          try {
            Navigator.of(context).pop();
            debugPrint('[PrePurchaseScreen] Modal closed after success');
          } catch (navError) {
            debugPrint('[PrePurchaseScreen] Could not close modal after success: $navError');
          }

          // Reset flag
          _isHandlingCartOperation = false;
        } else {
          // Unclear state - close modal and show warning
          debugPrint('[PrePurchaseScreen] ⚠️ Unclear cart state: ${cartStateAfter.status}');

          // Capture context before closing modal
          final rootContext = Navigator.of(context, rootNavigator: true).context;

          try {
            // Close the modal first
            Navigator.of(context).pop();
            debugPrint('[PrePurchaseScreen] Modal closed due to unclear state');
          } catch (navError) {
            debugPrint('[PrePurchaseScreen] Could not close modal: $navError');
          }

          // Show warning immediately in parent context
          try {
            ScaffoldMessenger.of(rootContext).showSnackBar(
              SnackBar(
                content: Text('Cart operation may have failed. Please check your cart.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
            debugPrint('[PrePurchaseScreen] Warning SnackBar shown');
          } catch (snackError) {
            debugPrint('[PrePurchaseScreen] Could not show warning SnackBar: $snackError');
          }

          // Reset flag
          _isHandlingCartOperation = false;
        }
      }
    } catch (e) {
      debugPrint('[PrePurchaseScreen] Exception during add to cart: $e');
      // Hide loading indicator if still showing
      if (context.mounted) {
        try {
          Navigator.of(context).pop(); // Try to close loading dialog
          debugPrint('[PrePurchaseScreen] Loading dialog closed');
        } catch (navError) {
          debugPrint('[PrePurchaseScreen] Could not close loading dialog: $navError');
        }

        // Capture context before closing modal
        final rootContext = Navigator.of(context, rootNavigator: true).context;

        try {
          // Close the modal
          Navigator.of(context).pop(); // Close the purchase modal
          debugPrint('[PrePurchaseScreen] Modal closed due to exception');
        } catch (navError) {
          debugPrint('[PrePurchaseScreen] Could not close modal: $navError');
        }

        // Show error immediately in parent context
        try {
          ScaffoldMessenger.of(rootContext).showSnackBar(
            SnackBar(
              content: Text('Failed to add to cart: $e'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 4),
            ),
          );
          debugPrint('[PrePurchaseScreen] Exception SnackBar shown');
        } catch (snackError) {
          debugPrint('[PrePurchaseScreen] Could not show exception SnackBar: $snackError');
        }

        // Reset flag
        _isHandlingCartOperation = false;
      }
    }
  }


}
