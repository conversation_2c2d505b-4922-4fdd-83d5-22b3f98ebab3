import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// Simple checkout WebView for buy now flow
/// No aggressive completion detection - only closes on manual exit or clear completion signals
class SimpleCheckoutWebView extends StatefulWidget {
  final String checkoutUrl;
  final VoidCallback? onCheckoutComplete;
  final VoidCallback? onCloseModal; // Add callback to close parent modal

  const SimpleCheckoutWebView({
    super.key,
    required this.checkoutUrl,
    this.onCheckoutComplete,
    this.onCloseModal,
  });

  @override
  State<SimpleCheckoutWebView> createState() => _SimpleCheckoutWebViewState();
}

class _SimpleCheckoutWebViewState extends State<SimpleCheckoutWebView> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    debugPrint('[SimpleCheckoutWebView] 🚀 INITIALIZING SIMPLE CHECKOUT WEBVIEW');
    debugPrint('[SimpleCheckoutWebView] Checkout URL: ${widget.checkoutUrl}');
    _initializeWebView();
  }

  @override
  void dispose() {
    debugPrint('[SimpleCheckoutWebView] 🧹 DISPOSING SIMPLE CHECKOUT WEBVIEW');
    super.dispose();
  }

  void _initializeWebView() {
    try {
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0x00000000))
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              // Update loading progress if needed
            },
            onPageStarted: (String url) {
              if (mounted) {
                setState(() {
                  _isLoading = true;
                });
              }
              debugPrint('[SimpleCheckoutWebView] Page started loading: $url');
            },
            onPageFinished: (String url) {
              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
              }
              debugPrint('[SimpleCheckoutWebView] Page finished loading: $url');

              // Only check for very clear completion signals
              if (_isDefinitelyComplete(url)) {
                debugPrint('[SimpleCheckoutWebView] ⚠️ DEFINITE COMPLETION DETECTED: $url');
                _handleCheckoutSuccess(url);
              }
            },
            onNavigationRequest: (NavigationRequest request) {
              debugPrint('[SimpleCheckoutWebView] Navigation to: ${request.url}');

              // Only detect very clear completion signals
              if (_isDefinitelyComplete(request.url)) {
                debugPrint('[SimpleCheckoutWebView] ⚠️ DEFINITE COMPLETION VIA NAVIGATION: ${request.url}');
                _handleCheckoutSuccess(request.url);
                return NavigationDecision.navigate;
              }

              // Allow all other navigation
              return NavigationDecision.navigate;
            },
            onWebResourceError: (WebResourceError error) {
              debugPrint('[SimpleCheckoutWebView] WebResource Error: ${error.description}');
              debugPrint('[SimpleCheckoutWebView] Error Type: ${error.errorType}');
              debugPrint('[SimpleCheckoutWebView] Error Code: ${error.errorCode}');

              // Log error but continue - most WebView errors are non-critical
              debugPrint('[SimpleCheckoutWebView] Continuing despite error...');
            },
            onHttpError: (HttpResponseError error) {
              debugPrint('[SimpleCheckoutWebView] HTTP Error: ${error.response?.statusCode}');
            },
            onUrlChange: (UrlChange change) {
              debugPrint('[SimpleCheckoutWebView] URL changed to: ${change.url}');
            },
          ),
        );

      // Load the checkout URL with error handling
      _controller.loadRequest(Uri.parse(widget.checkoutUrl)).catchError((error) {
        debugPrint('[SimpleCheckoutWebView] Error loading checkout URL: $error');
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      debugPrint('[SimpleCheckoutWebView] Error initializing WebView: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Only detect very clear, unambiguous completion signals
  bool _isDefinitelyComplete(String url) {
    debugPrint('[SimpleCheckoutWebView] Checking for definite completion: $url');

    // Extract the base domain from the original checkout URL
    final checkoutUri = Uri.tryParse(widget.checkoutUrl);
    final baseDomain = checkoutUri?.host ?? '';

    // Only trigger on very specific, clear completion patterns

    // Pattern 1: Redirect to home page after checkout completion (same as cart checkout)
    if ((url == 'https://$baseDomain/' || url == 'https://$baseDomain') &&
        !widget.checkoutUrl.contains(url)) { // Make sure it's not the original URL
      debugPrint('[SimpleCheckoutWebView] Detected redirect to home page - checkout likely completed');
      return true;
    }

    // Pattern 2: Explicit thank you pages
    if (url.contains('/thank_you') || url.contains('/checkout/thank_you')) {
      debugPrint('[SimpleCheckoutWebView] Thank you page detected');
      return true;
    }

    // Pattern 3: Order status pages with clear completion indicators
    if (url.contains('/orders/') && url.contains('/status') && url.contains('paid')) {
      debugPrint('[SimpleCheckoutWebView] Paid order status page detected');
      return true;
    }

    // Pattern 4: Explicit completion parameters
    if (url.contains('checkout_complete=true') || url.contains('payment_status=paid')) {
      debugPrint('[SimpleCheckoutWebView] Explicit completion parameter detected');
      return true;
    }

    // Pattern 5: Custom app deep links (if you implement them)
    if (url.startsWith('myapp://checkout/success')) {
      debugPrint('[SimpleCheckoutWebView] Deep link completion detected');
      return true;
    }

    // Pattern 6: Modern Shopify checkout completion patterns (same as cart checkout)
    final uri = Uri.tryParse(url);
    if (uri != null && uri.host.contains('myshopify.com')) {
      // Only trigger on actual home page redirect (not just any path)
      if ((uri.path == '/' || uri.path.isEmpty) && !url.contains('/checkouts/') && !url.contains('/cart/')) {
        debugPrint('[SimpleCheckoutWebView] Home page redirect detected - likely checkout completion');
        return true;
      }

      // Check for order confirmation pages
      if (uri.path.contains('/orders/') && (uri.path.contains('/status') || uri.path.contains('/confirmation'))) {
        debugPrint('[SimpleCheckoutWebView] Order confirmation page detected');
        return true;
      }
    }

    debugPrint('[SimpleCheckoutWebView] No definite completion detected');
    return false;
  }

  void _handleCheckoutSuccess(String url) {
    debugPrint('[SimpleCheckoutWebView] ⚠️ CHECKOUT SUCCESS TRIGGERED! URL: $url');
    debugPrint('[SimpleCheckoutWebView] This should only happen on definite completion!');

    if (mounted) {
      // Add a small delay to ensure the page loads completely before handling completion
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          debugPrint('[SimpleCheckoutWebView] Executing delayed completion handler');

          // Close the parent modal first (if provided)
          if (widget.onCloseModal != null) {
            debugPrint('[SimpleCheckoutWebView] Closing parent modal on checkout success');
            widget.onCloseModal!();
          }

          // Then handle checkout completion
          if (widget.onCheckoutComplete != null) {
            debugPrint('[SimpleCheckoutWebView] Calling onCheckoutComplete callback');
            widget.onCheckoutComplete!();
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[SimpleCheckoutWebView] 🎨 Building widget - isLoading: $_isLoading');
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Order'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _showExitConfirmation();
          },
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          // Wrap WebView in a Container to help with platform view stability
          Container(
            key: ValueKey('simple_checkout_container_${widget.checkoutUrl.hashCode}'),
            child: WebViewWidget(
              key: ValueKey('simple_checkout_webview_${widget.checkoutUrl.hashCode}'),
              controller: _controller,
            ),
          ),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'Loading checkout...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit Checkout?'),
          content: const Text(
            'Are you sure you want to exit? You can continue your purchase later.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog

                // Close parent modal if provided
                if (widget.onCloseModal != null) {
                  debugPrint('[SimpleCheckoutWebView] Closing parent modal on manual exit');
                  widget.onCloseModal!();
                }

                Navigator.of(context).pop(); // Close webview
              },
              child: const Text(
                'Exit',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
