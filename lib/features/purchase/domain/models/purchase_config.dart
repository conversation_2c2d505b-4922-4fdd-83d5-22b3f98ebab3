class PurchaseConfig {
  final String productId;
  final String productTitle;
  final String? productImageUrl;
  final String selectedData;
  final String selectedDays;
  final ESimType eSimType;
  final String? selectedIccid;
  final int quantity;
  final double unitPrice;
  final bool showDetails;

  PurchaseConfig({
    required this.productId,
    required this.productTitle,
    this.productImageUrl,
    this.selectedData = '1GB',
    this.selectedDays = '1 day',
    this.eSimType = ESimType.newESim,
    this.selectedIccid,
    this.quantity = 1,
    this.unitPrice = 1.00,
    this.showDetails = false,
  });

  double get totalPrice => unitPrice * quantity;

  PurchaseConfig copyWith({
    String? productId,
    String? productTitle,
    String? productImageUrl,
    String? selectedData,
    String? selectedDays,
    ESimType? eSimType,
    String? selectedIccid,
    int? quantity,
    double? unitPrice,
    bool? showDetails,
  }) {
    return PurchaseConfig(
      productId: productId ?? this.productId,
      productTitle: productTitle ?? this.productTitle,
      productImageUrl: productImageUrl ?? this.productImageUrl,
      selectedData: selectedData ?? this.selectedData,
      selectedDays: selectedDays ?? this.selectedDays,
      eSimType: eSimType ?? this.eSimType,
      selectedIccid: selectedIccid ?? this.selectedIccid,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      showDetails: showDetails ?? this.showDetails,
    );
  }
}

enum ESimType {
  newESim,
  topUp,
}

extension ESimTypeExtension on ESimType {
  String get displayName {
    switch (this) {
      case ESimType.newESim:
        return 'New eSIM';
      case ESimType.topUp:
        return 'Top Up';
    }
  }
}
