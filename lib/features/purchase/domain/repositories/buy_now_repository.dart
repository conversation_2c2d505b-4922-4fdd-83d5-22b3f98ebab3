import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/buy_now_response.dart';

/// Repository interface for Buy Now functionality
abstract class BuyNowRepository {
  /// Create a direct checkout for immediate purchase
  /// 
  /// Uses the simplified /api/buy_now endpoint that requires minimal parameters
  Future<Either<Failure, BuyNowResponse>> buyNow({
    required String variantId,
    required int quantity,
    String? productId,
    double? price,
    String? title,
  });

  /// Create a direct checkout with full validation
  /// 
  /// Uses the /api/direct_checkout endpoint that requires all parameters
  Future<Either<Failure, BuyNowResponse>> directCheckout({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
  });
}
