import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/buy_now_response.dart';
import '../repositories/buy_now_repository.dart';

/// Use case for Buy Now functionality
class BuyNowUseCase {
  final BuyNowRepository _repository;

  BuyNowUseCase(this._repository);

  /// Execute buy now with simplified parameters
  /// 
  /// This uses the /api/buy_now endpoint which requires minimal parameters
  Future<Either<Failure, BuyNowResponse>> call({
    required String variantId,
    required int quantity,
    String? productId,
    double? price,
    String? title,
  }) async {
    // Validate input parameters
    if (variantId.isEmpty) {
      return const Left(ValidationFailure(message: 'Variant ID is required'));
    }

    if (quantity <= 0) {
      return const Left(ValidationFailure(message: 'Quantity must be greater than 0'));
    }

    return await _repository.buyNow(
      variantId: variantId,
      quantity: quantity,
      productId: productId,
      price: price,
      title: title,
    );
  }
}

/// Use case for Direct Checkout functionality
class DirectCheckoutUseCase {
  final BuyNowRepository _repository;

  DirectCheckoutUseCase(this._repository);

  /// Execute direct checkout with full validation
  /// 
  /// This uses the /api/direct_checkout endpoint which requires all parameters
  Future<Either<Failure, BuyNowResponse>> call({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
  }) async {
    // Validate input parameters
    if (variantId.isEmpty) {
      return const Left(ValidationFailure(message: 'Variant ID is required'));
    }

    if (productId.isEmpty) {
      return const Left(ValidationFailure(message: 'Product ID is required'));
    }

    if (quantity <= 0) {
      return const Left(ValidationFailure(message: 'Quantity must be greater than 0'));
    }

    if (price <= 0) {
      return const Left(ValidationFailure(message: 'Price must be greater than 0'));
    }

    if (title.isEmpty) {
      return const Left(ValidationFailure(message: 'Title is required'));
    }

    return await _repository.directCheckout(
      variantId: variantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
    );
  }
}
