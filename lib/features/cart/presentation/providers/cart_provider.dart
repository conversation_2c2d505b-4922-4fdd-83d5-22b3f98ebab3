import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/core/network/api_config.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import '../../data/repositories/cart_repository_impl.dart';
import '../../domain/repositories/cart_repository.dart';
import 'cart_state.dart';

/// Provider for cart repository
final cartRepositoryProvider = Provider<CartRepository?>((ref) {
  try {
    final authRepository = ref.watch(authRepositoryProvider);
    if (authRepository == null) {
      return null;
    }
    return CartRepositoryImpl(
      dio: ref.watch(dioProvider),
      authRepository: authRepository,
      ref: ref,
    );
  } catch (e) {
    return null;
  }
});

/// Provider for Dio instance
final dioProvider = Provider<Dio>((ref) {
  return ApiConfig.createDioClient();
});

/// Provider for cart state
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  final repository = ref.watch(cartRepositoryProvider);

  if (repository == null) {
    return CartNotifier.unauthenticated();
  }

  final notifier = CartNotifier(repository, ref);
  return notifier;
});

/// Notifier for cart operations
class CartNotifier extends StateNotifier<CartState> {
  final CartRepository? _repository;
  final Ref? _ref;

  CartNotifier(this._repository, this._ref) : super(const CartState()) {
    // Listen to auth state changes and automatically fetch cart when authenticated
    if (_ref != null) {
      _ref.listen(authNotifierProvider, (previous, next) {
        next.maybeWhen(
          authenticated: (user) {
            if (user is EnhancedUserModel && user.hasBackendAuth && user.backendToken != null) {
              // User is authenticated with backend token, fetch cart
              getCart();
            }
          },
          orElse: () {},
        );
      });
    }
  }

  CartNotifier.unauthenticated() : _repository = null, _ref = null, super(const CartState(
    status: CartStatus.error,
    error: 'Authentication required',
    isAuthenticated: false,
  ));

  /// Handle connection errors by clearing tokens and showing appropriate error
  Future<void> _handleConnectionError(Failure failure) async {
    if (_ref == null) return;

    // Check if this is a connection error
    final isConnectionError = failure.message.contains('Connection refused') ||
        failure.message.contains('connection error') ||
        failure.message.contains('Network is unreachable') ||
        failure.message.contains('Failed to connect');

    if (isConnectionError) {
      debugPrint('🔥 [CartNotifier] Connection error detected: ${failure.message}');

      // Only clear tokens if this is a persistent connection error
      // (not on the first attempt to avoid aggressive token clearing)
      final currentState = state;
      final shouldClearTokens = currentState.status == CartStatus.error &&
                               currentState.error != null &&
                               currentState.error!.contains('Backend server is not available');

      if (shouldClearTokens) {
        debugPrint('🧹 [CartNotifier] Persistent connection error - clearing authentication tokens...');
        try {
          await _ref.read(authNotifierProvider.notifier).signOut();
          debugPrint('✅ [CartNotifier] Authentication tokens cleared successfully');
        } catch (e) {
          debugPrint('❌ [CartNotifier] Failed to clear tokens: $e');
        }
      } else {
        debugPrint('🔄 [CartNotifier] First connection attempt failed - keeping tokens for retry');
      }

      // Set error state with user-friendly message
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend server is not available. Please try again later.',
        isAuthenticated: !shouldClearTokens, // Keep auth state if not clearing tokens
        cart: null,
      );
    } else {
      // Handle other types of failures normally
      if (failure is AuthFailure) {
        state = state.copyWith(
          status: CartStatus.error,
          error: failure.message,
          isAuthenticated: false,
        );
      } else {
        state = state.copyWith(
          status: CartStatus.error,
          error: failure.message,
        );
      }
    }
  }

  /// Get the current cart
  Future<void> getCart() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.getCart();
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Add an item to the cart
  Future<void> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.addItem(
      variantId: variantId,
      productId: productId,
      quantity: quantity,
      price: price,
      title: title,
      currency: currency,
      imageUrl: imageUrl,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Update an item's quantity in the cart
  Future<void> updateItem({
    required String variantId,
    required int quantity,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.updateItem(
      variantId: variantId,
      quantity: quantity,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Remove an item from the cart
  Future<void> removeItem({
    required String variantId,
  }) async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.removeItem(
      variantId: variantId,
    );
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }

  /// Proceed to checkout
  Future<String?> checkout() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return null;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.checkout();
    return result.fold(
      (failure) {
        _handleConnectionError(failure);
        return null;
      },
      (successMessage) async {
        // Checkout succeeded, refetch cart to get updated state
        await getCart();
        return successMessage;
      },
    );
  }

  /// Clear the entire cart
  Future<void> clearCart() async {
    if (_repository == null || _ref == null) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Authentication required',
        isAuthenticated: false,
      );
      return;
    }

    // Check if user has backend authentication
    final authState = _ref.read(authNotifierProvider);
    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (!hasBackendAuth) {
      state = state.copyWith(
        status: CartStatus.error,
        error: 'Backend authentication required',
        isAuthenticated: false,
      );
      return;
    }

    state = state.copyWith(status: CartStatus.loading);
    final result = await _repository.clearCart();
    result.fold(
      (failure) => _handleConnectionError(failure),
      (cart) => state = state.copyWith(
        status: CartStatus.loaded,
        cart: cart,
        clearError: true, // Clear any previous errors
        isAuthenticated: true,
      ),
    );
  }
}