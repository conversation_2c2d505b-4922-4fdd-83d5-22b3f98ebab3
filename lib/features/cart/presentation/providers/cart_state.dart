import '../../domain/entities/cart.dart';

/// Cart status enum
enum CartStatus {
  initial,
  loading,
  loaded,
  error,
}

/// Cart state class
class CartState {
  final CartStatus status;
  final Cart? cart;
  final String? error;
  final bool isAuthenticated;

  const CartState({
    this.status = CartStatus.initial,
    this.cart,
    this.error,
    this.isAuthenticated = false,
  });

  CartState copyWith({
    CartStatus? status,
    Cart? cart,
    String? error,
    bool? isAuthenticated,
    bool clearError = false,
  }) {
    return CartState(
      status: status ?? this.status,
      cart: cart ?? this.cart,
      error: clearError ? null : (error ?? this.error),
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartState &&
        other.status == status &&
        other.cart == cart &&
        other.error == error &&
        other.isAuthenticated == isAuthenticated;
  }

  @override
  int get hashCode {
    return status.hashCode ^
        cart.hashCode ^
        error.hashCode ^
        isAuthenticated.hashCode;
  }

  @override
  String toString() {
    return 'CartState(status: $status, cart: $cart, error: $error, isAuthenticated: $isAuthenticated)';
  }
}