import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/cart.dart';
import '../../../../core/providers/product_provider.dart';
import '../../../../core/utils/shopify_utils.dart';

class CartItemCard extends ConsumerWidget {
  final CartItem item;
  final Function(int) onQuantityChanged;
  final VoidCallback onRemove;
  final bool isSelected;
  final Function(bool) onSelectionChanged;

  const CartItemCard({
    super.key,
    required this.item,
    required this.onQuantityChanged,
    required this.onRemove,
    this.isSelected = true,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the product image from the products provider
    final productsState = ref.watch(productsProvider);
    String? productImageUrl = item.imageUrl;

    // If cart item doesn't have image URL, try to find it from products
    if (productImageUrl == null || productImageUrl.isEmpty) {
      try {
        final product = productsState.products.firstWhere(
          (p) => _matchesProductId(p.id, item.productId),
        );
        productImageUrl = product.featuredImage?.url;
      } catch (e) {
        // Product not found in current products list
        productImageUrl = null;
      }
    }
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Checkbox
          SizedBox(
            width: 16,
            height: 16,
            child: Checkbox(
              value: isSelected,
              onChanged: (value) => onSelectionChanged(value ?? false),
              activeColor: const Color(0xFFFF6982),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              side: const BorderSide(
                color: Color(0xFFFF6982),
                width: 1,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Product Details Section
          Expanded(
            child: Row(
              children: [
                // Product Image (Carousel)
                Container(
                  width: 108,
                  height: 108,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xFFFF6982),
                  ),
                  child: _buildProductImage(productImageUrl),
                ),

                const SizedBox(width: 8),

                // Product Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Title
                      Text(
                        _getProductTitle(),
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF1F2937),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Data Detail
                      _buildDetailRow(_getDataAmount(), 'GB'),

                      const SizedBox(height: 8),

                      // Duration Detail
                      _buildDetailRow(_getDuration(), 'Day'),

                      const SizedBox(height: 8),

                      // Quantity Selector
                      _buildQuantitySelector(),

                      const SizedBox(height: 8),

                      // Price
                      _buildPriceSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
        ],
      ),
    );
  }

  Widget _buildProductImage(String? imageUrl) {
    return Stack(
      children: [
        // Main product image area
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: imageUrl != null && imageUrl.isNotEmpty
                ? Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Icon(
                          Icons.sim_card,
                          color: Colors.white,
                          size: 40,
                        ),
                      );
                    },
                  )
                : const Center(
                    child: Icon(
                      Icons.sim_card,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
          ),
        ),
        // Slide indicators at bottom
        Positioned(
          bottom: 8,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 2),
              Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 2),
              Container(
                width: 20,
                height: 6,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6982),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getProductTitle() {
    // Extract the main product name from the title
    final title = item.title;
    if (title.contains(' - ')) {
      return title.split(' - ').first;
    }
    return title;
  }

  String _getDataAmount() {
    // Extract data amount from title (e.g., "1GB")
    final title = item.title;
    final regex = RegExp(r'(\d+)GB');
    final match = regex.firstMatch(title);
    return match?.group(1) ?? '1';
  }

  String _getDuration() {
    // Extract duration from title (e.g., "1 day")
    final title = item.title;
    final regex = RegExp(r'(\d+)\s+day');
    final match = regex.firstMatch(title);
    return '${match?.group(1) ?? '1'} Days';
  }

Widget _buildDetailRow(String value, String label) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        label,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF9CA3AF),
        ),
      ),
      Text(
        value,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2937),
        ),
      ),
    ],
  );
}

  Widget _buildQuantitySelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Minus button (filled)
          GestureDetector(
            onTap: () => onQuantityChanged(item.quantity - 1),
            child: Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                color: Color(0xFFFF6982),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.remove,
                size: 16,
                color: Color(0xFFD1D5DB),
              ),
            ),
          ),

          // Quantity display
          Container(
            width: 30,
            height: 30,
            margin: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD1D5DB)),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '${item.quantity}',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ),
          ),

          // Plus button (outlined)
          GestureDetector(
            onTap: () => onQuantityChanged(item.quantity + 1),
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: const Color(0xFFFF6982),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.add,
                size: 16,
                color: Color(0xFFFF6982),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '\$${item.price.toStringAsFixed(2)}', // Price is already in dollars
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: const Color(0xFFFF6982),
          ),
        ),
      ],
    );
  }

  /// Check if product ID matches cart item product ID, handling both GID and numeric formats
  bool _matchesProductId(String productId, String cartItemProductId) {
    // Direct match
    if (productId == cartItemProductId) return true;

    // Extract numeric IDs and compare
    final productNumericId = ShopifyUtils.extractProductId(productId);
    final cartItemNumericId = ShopifyUtils.extractProductId(cartItemProductId);

    return productNumericId == cartItemNumericId;
  }
}
