import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_size_text/auto_size_text.dart';

class CartBottomInfo extends StatelessWidget {
  final bool isAllSelected;
  final Function(bool) onSelectAllChanged;
  final double totalPrice;
  final VoidCallback onCheckout;

  const CartBottomInfo({
    super.key,
    required this.isAllSelected,
    required this.onSelectAllChanged,
    required this.totalPrice,
    required this.onCheckout,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      child: Row(
        children: [
          // Select All Checkbox
          SizedBox(
            width: 16,
            height: 16,
            child: Checkbox(
              value: isAllSelected,
              onChanged: (value) => onSelectAllChanged(value ?? false),
              activeColor: const Color(0xFFFF6982),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              side: const BorderSide(
                color: Color(0xFF9CA3AF),
                width: 1,
              ),
            ),
          ),

          const SizedBox(width: 3),

          // Payment Section
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      // Total label
                      Text(
                        'Total:',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF1F2937),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Total price
                      Expanded(
                        child: AutoSizeText(
                          '\$${totalPrice.toStringAsFixed(2)}',
                          style: GoogleFonts.inter(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFFFF6982),
                          ),
                          textAlign: TextAlign.right,
                          maxLines: 1,
                          minFontSize: 12,
                          maxFontSize: 18,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Checkout Button
          Expanded(
            child: GestureDetector(
              onTap: onCheckout,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6982),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: const Color(0xFFFF6982),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text(
                        'Check out',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                          letterSpacing: 0.005,
                          height: 1.4285714285714286,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
