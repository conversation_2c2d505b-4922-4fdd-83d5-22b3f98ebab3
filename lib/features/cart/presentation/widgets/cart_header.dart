import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class CartHeader extends StatelessWidget implements PreferredSizeWidget {
  const CartHeader({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(86); // 44px status bar + 42px header body

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFF6982), // Pink color from Figma
      child: SafeArea(
        child: SizedBox(
          height: 42, // Header body height from Figma
          child: Row(
        children: [
          // Left icon container (52x42 with 5px 10px padding)
          Container(
            width: 52,
            height: 42,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: GestureDetector(
              onTap: () {
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/');
                }
              },
              child: Center(
                child: SvgPicture.asset(
                  'assets/icons/cart_header/arrow_left_correct.svg',
                  width: 30,
                  height: 30,
                  // colorFilter: const ColorFilter.mode(
                  //   Colors.white,
                  //   BlendMode.srcIn,
                  // ),
                ),
              ),
            ),
          ),

          // Center content with 40px left padding
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(left: 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 42,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 9),
                    child: Center(
                      child: Text(
                        'Cart',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Right icons container with 8px gap
          Container(
            height: 42,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
              children: [
                // Search icon with background
                GestureDetector(
                  onTap: () {
                    // TODO: Implement search functionality
                  },
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      // color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(150),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        'assets/icons/cart_header/search_correct.svg',
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(
                          Colors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Shopping bag icon with background
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    // color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(150),
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      'assets/icons/cart_header/shopping_bag_correct.svg',
                      width: 20,
                      height: 20,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
        ),
      ),
    );
  }
}
