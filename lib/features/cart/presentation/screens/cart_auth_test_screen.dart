import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import 'package:go_router/go_router.dart';

/// Test screen for testing cart authentication modal functionality
class CartAuthTestScreen extends ConsumerWidget {
  const CartAuthTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cart Auth Test'),
        backgroundColor: const Color(0xFFFF6982),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Current auth status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Authentication Status:',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    authState.when(
                      initial: () => 'Initial',
                      loading: () => 'Loading...',
                      authenticated: (user) => 'Authenticated: ${user.email}',
                      unauthenticated: () => 'Not Authenticated',
                      error: (failure) => 'Error: ${failure.message}',
                    ),
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: authState.maybeWhen(
                        authenticated: (_) => Colors.green,
                        unauthenticated: () => Colors.red,
                        error: (_) => Colors.red,
                        orElse: () => Colors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Sign out button (only show when authenticated)
            authState.maybeWhen(
              authenticated: (_) => ElevatedButton(
                onPressed: () async {
                  final authNotifier = ref.read(authNotifierProvider.notifier);
                  await authNotifier.signOut();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'Sign Out',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              orElse: () => const SizedBox.shrink(),
            ),

            const SizedBox(height: 16),

            // Test cart authentication button
            ElevatedButton(
              onPressed: () async {
                final isAuthenticated = await AuthGuardService.requireAuthForCart(
                  context,
                  ref,
                  onSuccess: () {
                    // Navigate to cart after successful authentication
                    debugPrint('[CartAuthTest] Authentication successful - user can now access cart');
                    if (context.mounted) {
                      context.go('/cart');
                    }
                  },
                );

                // If already authenticated, navigate directly
                if (isAuthenticated && context.mounted) {
                  context.go('/cart');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'Test Cart Authentication',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Direct cart navigation (for comparison)
            ElevatedButton(
              onPressed: () {
                context.go('/cart');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'Go to Cart (No Auth Check)',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Instructions:',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. If authenticated, click "Sign Out" first\n'
                    '2. Click "Test Cart Authentication"\n'
                    '3. Authentication modal should appear\n'
                    '4. Sign in and you should be redirected to cart',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
