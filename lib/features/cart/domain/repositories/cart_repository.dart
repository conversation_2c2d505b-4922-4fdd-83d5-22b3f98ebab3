import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/cart.dart';

/// Repository interface for cart operations
abstract class CartRepository {
  /// Get the current cart
  Future<Either<Failure, Cart>> getCart();

  /// Add an item to the cart
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  });

  /// Update an item's quantity in the cart
  Future<Either<Failure, Cart>> updateItem({
    required String variantId,
    required int quantity,
  });

  /// Remove an item from the cart
  Future<Either<Failure, Cart>> removeItem({
    required String variantId,
  });

  /// Proceed to checkout
  Future<Either<Failure, String>> checkout();

  /// Clear the entire cart
  Future<Either<Failure, Cart>> clearCart();
}