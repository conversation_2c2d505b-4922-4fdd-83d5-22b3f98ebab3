// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartItem _$CartItemFromJson(Map<String, dynamic> json) => CartItem(
  variantId: json['variant_id'] as String,
  productId: json['product_id'] as String,
  quantity: (json['quantity'] as num).toInt(),
  price: (json['price'] as num).toDouble(),
  title: json['title'] as String,
  imageUrl: json['image_url'] as String?,
);

Map<String, dynamic> _$CartItemToJson(CartItem instance) => <String, dynamic>{
  'variant_id': instance.variantId,
  'product_id': instance.productId,
  'quantity': instance.quantity,
  'price': instance.price,
  'title': instance.title,
  'image_url': instance.imageUrl,
};

Cart _$CartFromJson(Map<String, dynamic> json) => Cart(
  id: json['id'] as String?,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => CartItem.fromJson(e as Map<String, dynamic>))
          .toList(),
  totalPrice: (json['total_price'] as num).toDouble(),
  currency: json['currency'] as String?,
  itemsCount: (json['items_count'] as num).toInt(),
);

Map<String, dynamic> _$CartToJson(Cart instance) => <String, dynamic>{
  'id': instance.id,
  'items': instance.items,
  'total_price': instance.totalPrice,
  'currency': instance.currency,
  'items_count': instance.itemsCount,
};
