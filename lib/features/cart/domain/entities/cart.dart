import 'package:json_annotation/json_annotation.dart';

part 'cart.g.dart';

/// Cart item entity
@JsonSerializable()
class CartItem {
  @JsonKey(name: 'variant_id')
  final String variantId;
  @<PERSON><PERSON><PERSON>ey(name: 'product_id')
  final String productId;
  final int quantity;
  final double price;
  final String title;
  @JsonKey(name: 'image_url')
  final String? imageUrl;

  const CartItem({
    required this.variantId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.title,
    this.imageUrl,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => _$CartItemFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  CartItem copyWith({
    String? variantId,
    String? productId,
    int? quantity,
    double? price,
    String? title,
    String? imageUrl,
  }) {
    return CartItem(
      variantId: variantId ?? this.variantId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.variantId == variantId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.price == price &&
        other.title == title &&
        other.imageUrl == imageUrl;
  }

  @override
  int get hashCode {
    return variantId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        price.hashCode ^
        title.hashCode ^
        imageUrl.hashCode;
  }

  @override
  String toString() {
    return 'CartItem(variantId: $variantId, productId: $productId, quantity: $quantity, price: $price, title: $title, imageUrl: $imageUrl)';
  }
}

/// Cart entity
@JsonSerializable()
class Cart {
  final String? id;
  final List<CartItem> items;
  @JsonKey(name: 'total_price')
  final double totalPrice;
  final String? currency;
  @JsonKey(name: 'items_count')
  final int itemsCount;

  const Cart({
    this.id,
    required this.items,
    required this.totalPrice,
    this.currency,
    required this.itemsCount,
  });

  factory Cart.fromJson(Map<String, dynamic> json) => _$CartFromJson(json);

  Map<String, dynamic> toJson() => _$CartToJson(this);

  Cart copyWith({
    String? id,
    List<CartItem>? items,
    double? totalPrice,
    String? currency,
    int? itemsCount,
  }) {
    return Cart(
      id: id ?? this.id,
      items: items ?? this.items,
      totalPrice: totalPrice ?? this.totalPrice,
      currency: currency ?? this.currency,
      itemsCount: itemsCount ?? this.itemsCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Cart &&
        other.id == id &&
        other.items == items &&
        other.totalPrice == totalPrice &&
        other.currency == currency &&
        other.itemsCount == itemsCount;
  }

  @override
  int get hashCode {
    return id.hashCode ^ items.hashCode ^ totalPrice.hashCode ^ currency.hashCode ^ itemsCount.hashCode;
  }

  @override
  String toString() {
    return 'Cart(id: $id, items: $items, totalPrice: $totalPrice, currency: $currency, itemsCount: $itemsCount)';
  }
}