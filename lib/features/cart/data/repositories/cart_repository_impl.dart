import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/core/utils/shopify_utils.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_providers.dart';
import '../../domain/entities/cart.dart';
import '../../domain/repositories/cart_repository.dart';

/// Implementation of the cart repository
class CartRepositoryImpl implements CartRepository {
  final Dio _dio;
  final Ref _ref;

  CartRepositoryImpl({
    required Dio dio,
    required AuthRepository authRepository,
    required Ref ref,
  })  : _dio = dio,
        _ref = ref;

  /// Get the current authenticated user from auth state
  EnhancedUserModel? _getCurrentAuthenticatedUser() {
    final authState = _ref.read(authNotifierProvider);
    return authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          return state.user as EnhancedUserModel;
        }
        return null;
      },
      orElse: () => null,
    );
  }

  @override
  Future<Either<Failure, Cart>> getCart() async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;
      debugPrint('[CartRepository] Using backend token from EnhancedUserModel: ${backendToken.substring(0, 20)}...');

      final response = await _dio.get(
        '/cart',
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> addItem({
    required String variantId,
    required String productId,
    required int quantity,
    required double price,
    required String title,
    required String currency,
    String? imageUrl,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      // Extract numeric IDs from GID format for backend
      final numericVariantId = ShopifyUtils.extractVariantId(variantId);
      final numericProductId = ShopifyUtils.extractProductId(productId);

      debugPrint('[CartRepository] Converting GID to numeric ID:');
      debugPrint('[CartRepository] Variant: $variantId -> $numericVariantId');
      debugPrint('[CartRepository] Product: $productId -> $numericProductId');

      final response = await _dio.post(
        '/cart/add_item',
        data: {
          'variant_id': numericVariantId,
          'product_id': numericProductId,
          'quantity': quantity,
          'price': price,
          'title': title,
          'currency': currency,
          if (imageUrl != null) 'image_url': imageUrl,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> updateItem({
    required String variantId,
    required int quantity,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      // Use the exact variant ID format that exists in the cart
      // The backend should handle both GID and numeric formats
      debugPrint('[CartRepository] Updating item with variant ID: $variantId');

      final response = await _dio.patch(
        '/cart/update_item',
        data: {
          'variant_id': variantId, // Use exact format from cart
          'quantity': quantity,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> removeItem({
    required String variantId,
  }) async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      // Use the exact variant ID format that exists in the cart
      // The backend should handle both GID and numeric formats
      debugPrint('[CartRepository] Removing item with variant ID: $variantId');

      final response = await _dio.delete(
        '/cart/remove_item',
        data: {
          'variant_id': variantId, // Use exact format from cart
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> checkout() async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      final response = await _dio.post(
        '/cart/checkout',
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      // Handle response format - check if data exists and has checkout_url
      final data = response.data['data'] as Map<String, dynamic>?;
      final checkoutUrl = data?['checkout_url'] as String?;

      if (data == null || checkoutUrl == null || checkoutUrl.isEmpty) {
        // Checkout failed, no valid checkout URL
        final message = data?['message'] as String? ?? 'Checkout failed - no checkout URL received';
        return Left(ServerFailure(message: message));
      }

      // Checkout succeeded, return full JSON response for frontend processing
      // Create a success response format that the frontend expects
      final successResponse = {
        'success': true,
        'data': data,
        'message': data['message'] ?? 'Checkout URL created successfully'
      };
      return Right(json.encode(successResponse));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Cart>> clearCart() async {
    try {
      // Get current authenticated user from auth state
      final user = _getCurrentAuthenticatedUser();

      if (user == null) {
        debugPrint('[CartRepository] No authenticated user found');
        return const Left(AuthFailure(message: 'Authentication required'));
      }

      if (!user.hasBackendAuth || user.backendToken == null || user.backendToken!.isEmpty) {
        debugPrint('[CartRepository] User does not have backend authentication');
        return const Left(AuthFailure(message: 'Backend authentication required'));
      }

      final backendToken = user.backendToken!;

      final response = await _dio.delete(
        '/cart/clear',
        options: Options(
          headers: {
            'Authorization': 'Bearer $backendToken',
          },
        ),
      );

      // Parse the cart object from the response data
      final cartData = response.data['data'] as Map<String, dynamic>;
      return Right(Cart.fromJson(cartData));
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return const Left(AuthFailure(message: 'Unauthorized'));
      }
      return Left(ServerFailure(message: e.message ?? 'Server error'));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}