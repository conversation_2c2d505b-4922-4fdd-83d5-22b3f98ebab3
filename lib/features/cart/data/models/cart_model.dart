import 'package:json_annotation/json_annotation.dart';

part 'cart_model.g.dart';

@JsonSerializable()
class CartItemModel {
  final String variantId;
  final String productId;
  final int quantity;
  final double price;
  final String title;

  const CartItemModel({
    required this.variantId,
    required this.productId,
    required this.quantity,
    required this.price,
    required this.title,
  });

  factory CartItemModel.fromJson(Map<String, dynamic> json) =>
      _$CartItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemModelToJson(this);

  CartItemModel copyWith({
    String? variantId,
    String? productId,
    int? quantity,
    double? price,
    String? title,
  }) {
    return CartItemModel(
      variantId: variantId ?? this.variantId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      title: title ?? this.title,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItemModel &&
        other.variantId == variantId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.price == price &&
        other.title == title;
  }

  @override
  int get hashCode {
    return variantId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        price.hashCode ^
        title.hashCode;
  }

  @override
  String toString() {
    return 'CartItemModel(variantId: $variantId, productId: $productId, quantity: $quantity, price: $price, title: $title)';
  }
}

@JsonSerializable()
class CartModel {
  final List<CartItemModel> items;
  final double subtotal;
  final double tax;
  final double total;

  const CartModel({
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.total,
  });

  factory CartModel.fromJson(Map<String, dynamic> json) =>
      _$CartModelFromJson(json);

  Map<String, dynamic> toJson() => _$CartModelToJson(this);

  CartModel copyWith({
    List<CartItemModel>? items,
    double? subtotal,
    double? tax,
    double? total,
  }) {
    return CartModel(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      total: total ?? this.total,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartModel &&
        other.items == items &&
        other.subtotal == subtotal &&
        other.tax == tax &&
        other.total == total;
  }

  @override
  int get hashCode {
    return items.hashCode ^ subtotal.hashCode ^ tax.hashCode ^ total.hashCode;
  }

  @override
  String toString() {
    return 'CartModel(items: $items, subtotal: $subtotal, tax: $tax, total: $total)';
  }
}