import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'presentation/screens/account_screen.dart';
import 'presentation/screens/account_information_screen.dart';
import 'presentation/screens/transaction_history_screen.dart';
import '../../shared/widgets/bottom_nav_container.dart';

/// Routes for the account feature
class AccountRoutes {
  // Base path for all account routes
  static const String account = '/account';

  // Tab index for account in the bottom navigation
  static const int accountTabIndex = 4;

  // Sub-routes
  static const String accountInformation = 'information';
  static const String transactionHistory = 'transactions';
  static const String paymentSettings = 'payment-settings';
  static const String currency = 'currency';
  static const String languages = 'languages';
  static const String helpCenter = 'help-center';
  static const String privacyPolicy = 'privacy-policy';
  static const String termsConditions = 'terms-conditions';

  // Helper methods to get full paths
  static String getAccountInformationPath() => '$account/$accountInformation';
  static String getTransactionHistoryPath() => '$account/$transactionHistory';
  static String getPaymentSettingsPath() => '$account/$paymentSettings';
  static String getCurrencyPath() => '$account/$currency';
  static String getLanguagesPath() => '$account/$languages';
  static String getHelpCenterPath() => '$account/$helpCenter';
  static String getPrivacyPolicyPath() => '$account/$privacyPolicy';
  static String getTermsConditionsPath() => '$account/$termsConditions';

  // Define routes for GoRouter
  static List<RouteBase> get routes => [
    GoRoute(
      path: account,
      pageBuilder: (context, state) {
        return NoTransitionPage(
          child: BottomNavContainer(
            tabIndex: accountTabIndex,
            child: const AccountScreen(),
          ),
        );
      },
      routes: [
        GoRoute(
          path: accountInformation,
          builder: (context, state) => const AccountInformationScreen(),
        ),
        GoRoute(
          path: transactionHistory,
          builder: (context, state) => TransactionHistoryScreen(),
        ),
        // Placeholder routes for other sections
        GoRoute(
          path: paymentSettings,
          builder:
              (context, state) => Scaffold(
                appBar: AppBar(title: const Text('Payment Settings')),
                body: const Center(child: Text('Payment Settings Screen')),
              ),
        ),
        GoRoute(
          path: currency,
          builder:
              (context, state) => Scaffold(
                appBar: AppBar(title: const Text('Currency')),
                body: const Center(child: Text('Currency Screen')),
              ),
        ),
        GoRoute(
          path: languages,
          builder:
              (context, state) => Scaffold(
                appBar: AppBar(title: const Text('Languages')),
                body: const Center(child: Text('Languages Screen')),
              ),
        ),
        GoRoute(
          path: helpCenter,
          builder:
              (context, state) => Scaffold(
                appBar: AppBar(title: const Text('Help Center')),
                body: const Center(child: Text('Help Center Screen')),
              ),
        ),
        GoRoute(
          path: privacyPolicy,
          builder:
              (context, state) => Scaffold(
                appBar: AppBar(title: const Text('Privacy Policy')),
                body: const Center(child: Text('Privacy Policy Screen')),
              ),
        ),
        GoRoute(
          path: termsConditions,
          builder:
              (context, state) => Scaffold(
                appBar: AppBar(title: const Text('Terms & Conditions')),
                body: const Center(child: Text('Terms & Conditions Screen')),
              ),
        ),
      ],
    ),
  ];

  // Prevent instantiation
  AccountRoutes._();
}
