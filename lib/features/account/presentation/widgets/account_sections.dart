import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'account_action_list.dart';
import '../../account_routes.dart';

class AccountSections extends StatelessWidget {
  const AccountSections({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildPersonalSection(context),
        const SizedBox(height: 8),
        _buildPreferencesSection(context),
        const SizedBox(height: 8),
        _buildLegalSection(context),
      ],
    );
  }

  Widget _buildPersonalSection(BuildContext context) {
    return AccountActionList(
      items: [
        ActionItem(
          title: 'Account infomation',
          icon: 'assets/icons/profile_icon.svg',
          onTap: () => context.go(AccountRoutes.getAccountInformationPath()),
        ),
        ActionItem(
          title: 'Transaction history',
          icon: 'assets/icons/document_icon.svg',
          onTap: () => context.go(AccountRoutes.getTransactionHistoryPath()),
        ),
        ActionItem(
          title: 'Payment settings',
          icon: 'assets/icons/wallet_icon.svg',
          onTap: () => context.go(AccountRoutes.getPaymentSettingsPath()),
        ),
      ],
    );
  }

  Widget _buildPreferencesSection(BuildContext context) {
    return AccountActionList(
      items: [
        ActionItem(
          title: 'Currency',
          icon: 'assets/icons/star_icon.svg',
          onTap: () => context.go(AccountRoutes.getCurrencyPath()),
        ),
        ActionItem(
          title: 'Languages',
          icon: 'assets/icons/world_icon.svg',
          onTap: () => context.go(AccountRoutes.getLanguagesPath()),
        ),
        ActionItem(
          title: 'Help Center',
          icon: 'assets/icons/info_icon.svg',
          onTap: () => context.go(AccountRoutes.getHelpCenterPath()),
        ),
      ],
    );
  }

  Widget _buildLegalSection(BuildContext context) {
    return AccountActionList(
      items: [
        ActionItem(
          title: 'Privacy policy',
          icon: 'assets/icons/folder_lock_icon.svg',
          onTap: () => context.go(AccountRoutes.getPrivacyPolicyPath()),
        ),
        ActionItem(
          title: 'Terms & Conditions',
          icon: 'assets/icons/book_icon.svg',
          onTap: () => context.go(AccountRoutes.getTermsConditionsPath()),
        ),
      ],
    );
  }
}
