import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../auth/presentation/mixins/auth_redirect_mixin.dart';
import '../widgets/account_header.dart';
import '../widgets/account_sections.dart';
import '../widgets/logout_button.dart';
import '../widgets/app_version.dart';

class AccountScreen extends ConsumerStatefulWidget {
  const AccountScreen({super.key});

  @override
  ConsumerState<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends ConsumerState<AccountScreen> with AuthRedirectMixin {
  @override
  void initState() {
    super.initState();
    // Check authentication when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthentication();
    });
  }

  Future<void> _checkAuthentication() async {
    await AuthGuardService.requireAuthForAccount(context, ref);
  }

  @override
  Widget build(BuildContext context) {
    // Set up auth redirect listener in build method
    setupAuthRedirect();

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Account header as background layer
            Stack(
              children: [
                const AccountHeader(),

                // Main content with actions list on top
                Padding(
                  padding: const EdgeInsets.only(
                    top: 170,
                  ), // Adjust based on header height
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: const [
                        // All account sections
                        AccountSections(),

                        SizedBox(height: 8),

                        // Logout button
                        LogoutButton(),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // App version at bottom
            const AppVersion(version: '1.1.2'),

            // Bottom padding to ensure everything is visible when scrolling
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
