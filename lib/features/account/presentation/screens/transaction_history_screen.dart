import 'package:flutter/material.dart';

class TransactionHistoryScreen extends StatelessWidget {
  TransactionHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: AppBar(
        title: const Text('Transaction History'),
        backgroundColor: const Color(0xFFFF6982),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _demoTransactions.length,
        itemBuilder: (context, index) {
          final transaction = _demoTransactions[index];
          return _buildTransactionItem(transaction);
        },
      ),
    );
  }

  Widget _buildTransactionItem(Transaction transaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color:
                  transaction.type == TransactionType.purchase
                      ? const Color(0xFFFFEDEF)
                      : const Color(0xFFE6FFFA),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              transaction.type == TransactionType.purchase
                  ? Icons.shopping_cart_outlined
                  : Icons.local_activity_outlined,
              color:
                  transaction.type == TransactionType.purchase
                      ? const Color(0xFFFF6982)
                      : const Color(0xFF0D9488),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  transaction.date,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
          Text(
            transaction.amount,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color:
                  transaction.type == TransactionType.purchase
                      ? const Color(0xFFFF6982)
                      : const Color(0xFF0D9488),
            ),
          ),
        ],
      ),
    );
  }

  // Demo data with properly escaped dollar signs
  final List<Transaction> _demoTransactions = [
    Transaction(
      title: 'Thailand eSIM Purchase',
      date: 'May 12, 2023',
      amount: '-\$29.99',
      type: TransactionType.purchase,
    ),
    Transaction(
      title: 'Travel Reward Points',
      date: 'May 10, 2023',
      amount: '+500 pts',
      type: TransactionType.reward,
    ),
    Transaction(
      title: 'Japan eSIM Purchase',
      date: 'Apr 25, 2023',
      amount: '-\$39.99',
      type: TransactionType.purchase,
    ),
    Transaction(
      title: 'Monthly Subscription',
      date: 'Apr 15, 2023',
      amount: '-\$9.99',
      type: TransactionType.purchase,
    ),
    Transaction(
      title: 'Referral Bonus',
      date: 'Apr 05, 2023',
      amount: '+1000 pts',
      type: TransactionType.reward,
    ),
    Transaction(
      title: 'Vietnam eSIM Purchase',
      date: 'Mar 22, 2023',
      amount: '-\$19.99',
      type: TransactionType.purchase,
    ),
  ];
}

enum TransactionType { purchase, reward }

class Transaction {
  final String title;
  final String date;
  final String amount;
  final TransactionType type;

  Transaction({
    required this.title,
    required this.date,
    required this.amount,
    required this.type,
  });
}
