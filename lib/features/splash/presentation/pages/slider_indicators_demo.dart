import 'package:flutter/material.dart';
import 'package:flutter_travelgator/features/splash/presentation/widgets/slider_indicator.dart';
import 'package:flutter_travelgator/features/splash/presentation/widgets/animated_slider_indicator.dart';
import 'package:flutter_travelgator/features/splash/presentation/widgets/sliding_dot_indicator.dart';

class SliderIndicatorsDemo extends StatefulWidget {
  const SliderIndicatorsDemo({super.key});

  @override
  State<SliderIndicatorsDemo> createState() => _SliderIndicatorsDemoState();
}

class _SliderIndicatorsDemoState extends State<SliderIndicatorsDemo> {
  int _currentPage = 0;
  final int _totalPages = 5;

  void _nextPage() {
    setState(() {
      _currentPage = (_currentPage + 1) % _totalPages;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.blueGrey[900],
      appBar: AppBar(
        title: const Text('Slider Indicators'),
        backgroundColor: Colors.blueGrey[800],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Basic Indicator'),
            _buildIndicatorContainer(
              SliderIndicator(
                currentPage: _currentPage,
                totalPages: _totalPages,
              ),
            ),

            _buildSectionTitle('Animated Indicator'),
            _buildIndicatorContainer(
              AnimatedSliderIndicator(
                currentPage: _currentPage,
                totalPages: _totalPages,
                activeColor: Colors.white,
                inactiveColor: Colors.white.withAlpha(128),
                dotSize: 6.0,
                spacing: 5.0,
              ),
            ),

            _buildSectionTitle('Sliding Dot Indicator'),
            _buildIndicatorContainer(
              SlidingDotIndicator(
                currentPage: _currentPage,
                totalPages: _totalPages,
                activeColor: Colors.white,
                inactiveColor: Colors.white.withAlpha(128),
                dotSize: 8.0,
                spacing: 16.0,
              ),
            ),

            _buildSectionTitle('Bouncing Dot Indicator'),

            const Spacer(),

            Center(
              child: ElevatedButton(
                onPressed: _nextPage,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueGrey[700],
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32.0,
                    vertical: 16.0,
                  ),
                ),
                child: const Text('Next Page'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24.0, bottom: 12.0),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildIndicatorContainer(Widget indicator) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      decoration: BoxDecoration(
        color: Colors.blueGrey[800],
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Center(child: indicator),
    );
  }
}
