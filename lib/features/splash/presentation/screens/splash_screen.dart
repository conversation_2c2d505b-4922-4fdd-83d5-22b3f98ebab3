import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_travelgator/routes/app_router.dart';
import 'package:flutter_travelgator/routes/provider/router_state_provider.dart';

// Provider to simulate app loading process
final appLoadingProvider = FutureProvider<bool>((ref) async {
  // Simulate app initialization/loading process
  await Future.delayed(const Duration(seconds: 2));
  return true;
});

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // For testing: uncomment the line below to reset onboarding state
    _resetOnboardingForTesting();
  }

  void _resetOnboardingForTesting() async {
    try {
      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Wait for router state to be initialized
      await ref.read(routerStateProvider.future);

      // Check again if widget is still mounted after async operation
      if (!mounted) return;

      // Reset onboarding state for testing
      await ref.read(routerStateProvider.notifier).resetOnboarding();
      debugPrint('Onboarding state reset for testing');
    } catch (e) {
      debugPrint('Error resetting onboarding state: $e');
    }
  }

  void _navigateToNextScreen() async {
    try {
      // Wait for router state to be properly initialized
      await ref.read(routerStateProvider.future);

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Check if onboarding is completed
      final routerStateNotifier = ref.read(routerStateProvider.notifier);
      final isOnboardingCompleted = routerStateNotifier.isOnboardingCompleted();

      debugPrint('Onboarding completed: $isOnboardingCompleted');

      if (isOnboardingCompleted) {
        // User has already seen onboarding, go to home
        debugPrint('Navigating to home');
        if (mounted) context.go(AppRoutes.home);
      } else {
        // First time user, show onboarding
        debugPrint('Navigating to onboarding');
        if (mounted) context.go(AppRoutes.onboarding);
      }
    } catch (e) {
      debugPrint('Error during navigation: $e');
      // Fallback to onboarding if there's an error
      if (mounted) context.go(AppRoutes.onboarding);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    // Listen to the loading state and navigate when it completes
    ref.listen<AsyncValue<bool>>(appLoadingProvider, (_, state) {
      if (state is AsyncData<bool> && state.value == true) {
        _navigateToNextScreen();
      }
    });

    return Scaffold(
      backgroundColor: Colors.white, // White background as specified
      body: SizedBox(
        width: size.width,
        height: size.height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo using SVG
            SvgPicture.asset(
              'assets/images/travelgator_logo.svg',
              width: 200,
              height: 35,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 48),
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6982)),
              strokeWidth: 3,
            ),
          ],
        ),
      ),
    );
  }
}
