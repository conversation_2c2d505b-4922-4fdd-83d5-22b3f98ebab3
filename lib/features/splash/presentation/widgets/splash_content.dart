import 'package:flutter/material.dart';
import 'package:flutter_travelgator/features/splash/presentation/widgets/animated_slider_indicator.dart';

class SplashContent extends StatelessWidget {
  final String title;
  final String subtitle;
  final int currentPage;
  final int totalPages;

  const SplashContent({
    super.key,
    required this.title,
    required this.subtitle,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 26,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                subtitle,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.centerLeft,
                child: AnimatedSliderIndicator(
                  currentPage: currentPage,
                  totalPages: totalPages,
                  activeColor: Colors.white,
                  inactiveColor: Colors.white.withAlpha(128),
                  dotSize: 6.0,
                  spacing: 5.0,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
