import 'package:flutter/material.dart';

class SliderIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;

  const SliderIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          clipBehavior: Clip.antiAlias,
          decoration: const BoxDecoration(),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: List.generate(
              totalPages,
              (index) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 5,
                  height: 5,
                  curve: Curves.easeInOut,
                  decoration: BoxDecoration(
                    color:
                        currentPage == index
                            ? Colors.white
                            : const Color(
                              0xFFF9FAFB,
                            ).withAlpha(153), // ~0.6 opacity
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
