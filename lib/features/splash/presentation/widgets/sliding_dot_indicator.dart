import 'package:flutter/material.dart';

class SlidingDotIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Color activeColor;
  final Color inactiveColor;
  final double dotSize;
  final double spacing;

  const SlidingDotIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.activeColor = Colors.white,
    this.inactiveColor = Colors.grey,
    this.dotSize = 8.0,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: dotSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background dots
          Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              totalPages,
              (index) => Padding(
                padding: EdgeInsets.symmetric(horizontal: spacing / 2),
                child: Container(
                  width: dotSize,
                  height: dotSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: inactiveColor,
                  ),
                ),
              ),
            ),
          ),

          // Animated active dot
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            left: currentPage * (dotSize + spacing),
            child: Container(
              width: dotSize,
              height: dotSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: activeColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
