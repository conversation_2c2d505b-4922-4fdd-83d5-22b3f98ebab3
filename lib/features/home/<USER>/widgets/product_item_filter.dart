import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/country.dart';
import 'travel_sim_card.dart';
import '../../../../../core/providers/countries_provider.dart';

class ProductItemFilter extends ConsumerStatefulWidget {
  const ProductItemFilter({super.key});

  @override
  ConsumerState<ProductItemFilter> createState() => _ProductItemFilterState();
}

class _ProductItemFilterState extends ConsumerState<ProductItemFilter> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final countriesState = ref.watch(countriesStateProvider);
    // Display all countries without any active selection
    
    if (countriesState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFF6982)),
      );
    }

    if (countriesState.error != null) {
      return Center(
        child: Text(
          'Error loading countries: ${countriesState.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (countriesState.countries.isEmpty) {
      return const Center(child: Text('No countries available'));
    }

    return _buildCountriesList(
      context,
      ref,
      countriesState.countries,
    );
  }

  Widget _buildCountriesList(
    BuildContext context,
    WidgetRef ref,
    List<Country> countries,
  ) {
    return Column(
      children:
          countries.map((country) {
            // All cards are non-active
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: TravelSimCard(
                country: country,
                isActive: false, // No card is ever active
                onTap: () {}, // Empty function instead of null
              ),
            );
          }).toList(),
    );
  }
}
