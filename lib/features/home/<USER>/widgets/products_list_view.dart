import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'product_card.dart';
import 'product_shimmer.dart';
import '../../../../../core/providers/product_provider.dart';
import '../../../../../core/models/product_model.dart';
import '../../../../../core/utils/price_formatter.dart';
import '../../../purchase/presentation/screens/pre_purchase_screen.dart';

class ProductsListView extends ConsumerStatefulWidget {
  const ProductsListView({super.key});

  @override
  ConsumerState<ProductsListView> createState() => _ProductsListViewState();
}

class _ProductsListViewState extends ConsumerState<ProductsListView> {

  // Fixed height for both product card and shimmer to ensure consistent height
  static const double _cardHeight = 250;

  @override
  Widget build(BuildContext context) {
    final productsState = ref.watch(productsProvider);

    return Container(
      padding: const EdgeInsets.only(left: 14, right: 14),
      alignment: Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            child: Row(mainAxisAlignment: MainAxisAlignment.end),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: _cardHeight,
            child:
                productsState.error != null
                    ? Center(
                      child: Text(
                        'Error: ${productsState.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    )
                    : productsState.products.isEmpty && !productsState.isLoading
                    ? const Center(child: Text('No products found'))
                    : productsState.products.isEmpty && productsState.isLoading
                    ? _buildShimmerList() // Only shimmers for initial loading
                    : _buildProductsList(context, productsState),
          ),
          if (productsState.hasNextPage && !productsState.isLoading)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Center(
                child: ElevatedButton(
                  onPressed: () {
                    ref
                        .read(productsProvider.notifier)
                        .fetchMoreProducts(first: 5);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF6982),
                  ),
                  child: const Text('Load More'),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildShimmerList() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: 5, // Show 5 shimmer items while loading
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(right: index == 4 ? 0 : 8),
          child: SizedBox(height: _cardHeight, child: const ProductShimmer()),
        );
      },
    );
  }

  Widget _buildProductsList(BuildContext context, dynamic productsState) {
    // Calculate the list of items to show:
    // - All loaded products
    // - Additionally, shimmer loaders if we're loading more products
    final List<Widget> items = [];

    // Add actual products
    for (int i = 0; i < productsState.products.length; i++) {
      final product = productsState.products[i];

      // Get price information using utility function
      final priceInfo = PriceFormatter.getProductPriceInfo(product);
      final price = priceInfo.currentPrice;
      final originalPrice = priceInfo.originalPrice;

      items.add(
        Padding(
          padding: EdgeInsets.only(
            right:
                (i == productsState.products.length - 1) &&
                        !productsState.isLoading
                    ? 0
                    : 8,
          ),
          child: SizedBox(
            height: _cardHeight,
            child: ProductCard(
              imageUrl:
                  product.featuredImage?.url ?? 'assets/images/card_image1.png',
              title: product.title,
              price: price,
              originalPrice: originalPrice,
              onTap: () {
                _showPrePurchaseModal(context, product);
              },
            ),
          ),
        ),
      );
    }

    // Add shimmer loaders if we're loading more products
    if (productsState.isLoading) {
      // Add 3 shimmer loaders
      for (int i = 0; i < 3; i++) {
        final isLast = i == 2;
        items.add(
          Padding(
            padding: EdgeInsets.only(right: isLast ? 0 : 8),
            child: SizedBox(height: _cardHeight, child: const ProductShimmer()),
          ),
        );
      }
    }

    return ListView(scrollDirection: Axis.horizontal, children: items);
  }

  Future<void> _showPrePurchaseModal(BuildContext context, Product product) async {
    // Show the pre-purchase modal directly without authentication check
    // Authentication will be required only when user clicks Add to Cart or Buy Now
    if (mounted) {
      _displayPrePurchaseModal(context, product);
    }
  }

  void _displayPrePurchaseModal(BuildContext context, Product product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (BuildContext context) {
        return PrePurchaseScreen(product: product);
      },
    );
  }
}
