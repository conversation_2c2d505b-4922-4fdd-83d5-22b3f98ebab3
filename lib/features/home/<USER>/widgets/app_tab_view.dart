import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/tab_state_provider.dart';
import 'home_bottom_nav_bar.dart';

/// A reusable container widget that manages tab-based navigation with bottom navigation bar
class AppTabView extends ConsumerWidget {
  /// List of screens to display in the tab view
  final List<Widget> screens;

  /// Optional callback to handle tab changes
  final Function(int)? onTabChanged;

  /// Initial tab index (defaults to the current tab state)
  final int? initialTabIndex;

  const AppTabView({
    super.key,
    required this.screens,
    this.onTabChanged,
    this.initialTabIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use initialTabIndex if provided, otherwise use the current tab state
    if (initialTabIndex != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(tabStateProvider.notifier).setTab(initialTabIndex!);
      });
    }

    final selectedTabIndex = ref.watch(tabStateProvider);

    // Set up a listener for tab changes
    ref.listen(tabStateProvider, (previous, current) {
      // Call the onTabChanged callback if provided
      if (onTabChanged != null) {
        onTabChanged!(current);
      }
    });

    return Scaffold(
      body: IndexedStack(index: selectedTabIndex, children: screens),
      bottomNavigationBar: const HomeBottomNavBar(),
    );
  }
}
