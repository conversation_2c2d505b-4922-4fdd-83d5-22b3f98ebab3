import 'package:flutter/material.dart';
import '../screens/home_screen.dart';
import '../screens/sims_screen.dart';
import '../screens/travelgator_screen.dart';
import '../screens/rewards_screen.dart';
import '../../../../features/account/presentation/screens/account_screen.dart';
import 'app_tab_view.dart';

/// Main app navigation using the reusable AppTabView component
class MainAppTabView extends StatelessWidget {
  const MainAppTabView({super.key});

  @override
  Widget build(BuildContext context) {
    // Define screens for each tab
    final screens = [
      const HomeScreen(),
      const SimsScreen(),
      const TravelgatorScreen(),
      const RewardsScreen(),
      const AccountScreen(),
    ];

    return AppTabView(screens: screens);
  }
}
