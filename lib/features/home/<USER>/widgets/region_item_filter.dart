import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/providers/regions_provider.dart';
import 'travel_region_card.dart';

class RegionItemFilter extends ConsumerWidget {
  const RegionItemFilter({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final regionsState = ref.watch(regionsStateProvider);

    if (regionsState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFF6982)),
      );
    }

    if (regionsState.error != null) {
      return Center(
        child: Text(
          'Error loading regions: ${regionsState.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (regionsState.regions.isEmpty) {
      return const Center(child: Text('No regions available'));
    }

    return Column(
      children:
          regionsState.regions.map((region) {
            final name = region.name;
            final description = region.description;
            final code = region.code;

            // All regions are displayed as non-active
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: TravelRegionCard(
                name: name,
                description: description,
                price: '0',
                code: code,
                isActive: false, // Never show as active
                onTap: () {}, // Empty function
              ),
            );
          }).toList(),
    );
  }
}
