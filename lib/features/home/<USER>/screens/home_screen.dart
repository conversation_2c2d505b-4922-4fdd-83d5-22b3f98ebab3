import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../widgets/product_item_filter.dart';
import '../widgets/featured_banner_card.dart';
import '../widgets/home_search_bar.dart';
import '../widgets/filter_item.dart';
import '../widgets/products_list_view.dart';
import '../widgets/region_item_filter.dart';
import '../../../../../core/providers/product_provider.dart';
import '../../../../../core/providers/regions_provider.dart';
import '../../../../../core/providers/countries_provider.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  bool _isCountriesSelected = true;
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;

  @override
  void initState() {
    super.initState();
    // Fetch products, countries and regions when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) {
        return; // Add mounted check to prevent ref usage after disposal
      }

      // Load all data in parallel for faster initial load
      Future.wait([
        ref.read(productsProvider.notifier).fetchProducts(first: 5),
        ref.read(countriesStateProvider.notifier).fetchCountries(),
      ]);
    });

    // Listen to scroll position to show/hide scroll-to-top button
    _scrollController.addListener(() {
      if (_scrollController.offset >= 200 && !_showScrollToTop) {
        setState(() {
          _showScrollToTop = true;
        });
      } else if (_scrollController.offset < 200 && _showScrollToTop) {
        setState(() {
          _showScrollToTop = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Refresh data by re-fetching products, countries and regions
  Future<void> _refreshData() async {
    // Reset all states to initial values
    setState(() {
      _isCountriesSelected = true;
    });

    // Clear any cached products and reset all filters in parallel
    await Future.wait([
      ref.read(productsProvider.notifier).clearAndFetchProducts(),
      ref.read(countriesStateProvider.notifier).forceRefreshCountries(),
      ref.read(regionsStateProvider.notifier).forceRefreshRegions(),
    ]);
  }

  // Scroll to top function
  void _scrollToTop() {
    // First scroll to the top
    _scrollController
        .animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        )
        .then((_) {
          // Add a tiny delay before refreshing to make the action more noticeable
          Future.delayed(const Duration(milliseconds: 100), () {
            // Completely refresh data after scrolling to top
            _refreshData();
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFF6982),
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const HomeSearchBar(),
      ),
      // Add floating action button for scroll-to-top
      floatingActionButton:
          _showScrollToTop
              ? FloatingActionButton(
                onPressed: _scrollToTop,
                backgroundColor: const Color(0xFFFF6982),
                mini: true,
                tooltip: 'Refresh home screen',
                child: const Icon(Icons.keyboard_arrow_up),
              )
              : null,
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: const Color(0xFFFF6982),
        displacement: 30,
        strokeWidth: 3,
        triggerMode: RefreshIndicatorTriggerMode.anywhere,
        child: SingleChildScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // Featured banner
              const Padding(
                padding: EdgeInsets.only(left: 14, right: 14, top: 14),
                child: FeaturedBannerCard(),
              ),

              // Featured products section
              const ProductsListView(),

              // Countries section
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 14,
                  vertical: 8,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Restore filter toggle buttons
                    Padding(
                      padding: const EdgeInsets.all(5),
                      child: FilterItem(
                        isCountriesSelected: _isCountriesSelected,
                        onCountriesPressed: () {
                          setState(() {
                            _isCountriesSelected = true;
                          });
                          // Load countries data
                          ref.read(countriesStateProvider.notifier).fetchCountries();
                        },
                        onRegionsPressed: () {
                          setState(() {
                            _isCountriesSelected = false;
                          });
                          // Load regions data
                          ref.read(regionsStateProvider.notifier).fetchRegions();
                        },
                      ),
                    ),
                    
                    const SizedBox(height: 8),

                    // Show either country or region filter based on selection
                    _isCountriesSelected
                        ? const ProductItemFilter()
                        : const RegionItemFilter(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
