import 'package:flutter/material.dart';

class TravelgatorScreen extends StatelessWidget {
  const TravelgatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: AppBar(
        title: const Text('TravelGator'),
        backgroundColor: const Color(0xFFFF6982),
      ),
      body: Center(
        child: Text(
          'TravelGator Screen',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
      ),
    );
  }
}
