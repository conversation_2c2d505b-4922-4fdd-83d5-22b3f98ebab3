import 'package:flutter/material.dart';

class Country {
  final String name;
  final String description;
  final String price;
  final String flagPath;
  final String code;
  final String? id;
  final String? handle;

  Country({
    required String name,
    required this.description,
    required this.price,
    required this.code,
    this.id,
    this.handle,
  }) : flagPath = 'assets/images/flags/${code.toLowerCase()}.svg',
       name = name.isEmpty ? code.toUpperCase() : name;

  /// Create a Country directly with ISO country code
  factory Country.fromIsoCode({
    required String code,
    required String description,
    required String price,
  }) {
    return Country(
      name: code.toUpperCase(),
      description: description,
      price: price,
      code: code.toLowerCase(),
    );
  }

  /// Create a Country from Shopify metaobject data
  factory Country.fromShopify(Map<String, dynamic> data) {
    // Check if data is a node from the edges array
    final node =
        data.containsKey('node') ? data['node'] as Map<String, dynamic> : data;

    // Extract ID and handle
    final String id = node['id'] as String? ?? '';
    final String handle = node['handle'] as String? ?? '';

    // Extract fields
    final Map<String, String> fields = {};

    if (node.containsKey('fields')) {
      if (node['fields'] is List) {
        final List<dynamic> fieldsArray = node['fields'] as List<dynamic>;
        for (final field in fieldsArray) {
          if (field is Map<String, dynamic> &&
              field.containsKey('key') &&
              field.containsKey('value')) {
            final key = field['key'] as String;
            final value = field['value'] as String?;
            if (value != null) {
              fields[key] = value;
            }
          }
        }
      } else if (node['fields'] is Map) {
        final Map<String, dynamic> fieldMap =
            node['fields'] as Map<String, dynamic>;
        fieldMap.forEach((key, value) {
          if (value != null && value is String) {
            fields[key] = value;
          }
        });
      }
    }

    // Get country code either from fields or from handle
    final String code =
        fields['code'] ?? (handle.isNotEmpty ? handle.split('-').first : '');

    return Country(
      id: id,
      handle: handle,
      name: fields['name'] ?? code.toUpperCase(),
      description: fields['description'] ?? '',
      price: fields['price'] ?? '\$0',
      code: code,
    );
  }

  /// Create a list of Country objects from Shopify metaobjects GraphQL response
  static List<Country> fromShopifyList(Map<String, dynamic> response) {
    final List<Country> countries = [];
    if (response.containsKey('metaobjects') &&
        response['metaobjects'].containsKey('nodes')) {
      final List<dynamic> nodes =
          response['metaobjects']['nodes'] as List<dynamic>;

      for (final node in nodes) {
        if (node is Map<String, dynamic>) {
          countries.add(
            Country.fromShopify(node),
          );
        }
      }
    }

    debugPrint('Countries parsed: ${countries.length}');
    return countries;
  }

  /// Helper method to get flag path directly from code
  static String getFlagPathByCode(String code) {
    return 'assets/images/flags/${code.toLowerCase()}.svg';
  }

  @override
  String toString() {
    return 'Country{name: $name, code: $code, price: $price}';
  }
}
