import 'package:flutter/material.dart';
import 'presentation/widgets/main_app_tab_view.dart';

class HomeRoute {
  static const String home = '/home';
  static const int homeTabIndex = 0; // Index for home tab

  static Map<String, WidgetBuilder> routes() {
    return {
      home: (context) => const MainAppTabView(),
      // Example of how a standalone home sub-route could use BottomNavContainer:
      // '/home/<USER>': (context) => BottomNavContainer(
      //   tabIndex: homeTabIndex,
      //   child: HomeDetailsScreen(),
      // ),
    };
  }
}
