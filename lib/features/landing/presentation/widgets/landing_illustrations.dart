import 'package:flutter/material.dart';

/// Landing illustrations widget for displaying visual elements
class LandingIllustrations extends StatelessWidget {
  final int currentPage;
  final Color backgroundColor;

  const LandingIllustrations({
    super.key,
    required this.currentPage,
    this.backgroundColor = const Color(0xFFFF6982),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 300,
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: _buildIllustration(),
      ),
    );
  }

  Widget _buildIllustration() {
    switch (currentPage) {
      case 0:
        return _buildWelcomeIllustration();
      case 1:
        return _buildConnectivityIllustration();
      case 2:
        return _buildExploreIllustration();
      default:
        return _buildWelcomeIllustration();
    }
  }

  Widget _buildWelcomeIllustration() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.travel_explore,
          size: 80,
          color: backgroundColor.withValues(alpha: 0.8),
        ),
        const SizedBox(height: 16),
        Text(
          'Welcome',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: backgroundColor.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildConnectivityIllustration() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.wifi,
          size: 80,
          color: backgroundColor.withValues(alpha: 0.8),
        ),
        const SizedBox(height: 16),
        Text(
          'Stay Connected',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: backgroundColor.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildExploreIllustration() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.explore,
          size: 80,
          color: backgroundColor.withValues(alpha: 0.8),
        ),
        const SizedBox(height: 16),
        Text(
          'Explore',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: backgroundColor.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }
}
