// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'landing_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$landingNotifierHash() => r'e64c2de64243baacfa2a26dd56618799e41da762';

/// Landing page state notifier
///
/// Copied from [LandingNotifier].
@ProviderFor(LandingNotifier)
final landingNotifierProvider =
    AutoDisposeNotifierProvider<LandingNotifier, LandingState>.internal(
      LandingNotifier.new,
      name: r'landingNotifierProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$landingNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LandingNotifier = AutoDisposeNotifier<LandingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
