import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'landing_state_provider.g.dart';

/// State for landing screen
class LandingState {
  final int currentPage;
  final bool isCompleted;

  const LandingState({
    this.currentPage = 0,
    this.isCompleted = false,
  });

  LandingState copyWith({
    int? currentPage,
    bool? isCompleted,
  }) {
    return LandingState(
      currentPage: currentPage ?? this.currentPage,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

/// Landing page state notifier
@riverpod
class LandingNotifier extends _$LandingNotifier {
  @override
  LandingState build() {
    return const LandingState();
  }

  /// Navigate to a specific page
  void goToPage(int page) {
    if (page >= 0 && page < 3) {
      state = state.copyWith(currentPage: page);
    }
  }

  /// Go to next page
  void nextPage() {
    if (state.currentPage < 2) {
      state = state.copyWith(currentPage: state.currentPage + 1);
    }
  }

  /// Go to previous page
  void previousPage() {
    if (state.currentPage > 0) {
      state = state.copyWith(currentPage: state.currentPage - 1);
    }
  }

  /// Complete landing and mark it as done
  void completeLanding() {
    state = state.copyWith(isCompleted: true);
  }
}
