import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_travelgator/routes/app_router.dart';
import '../providers/landing_state_provider.dart';
import '../widgets/landing_page.dart';
import '../widgets/landing_page_indicator.dart';

class LandingScreen extends ConsumerStatefulWidget {
  const LandingScreen({super.key});

  @override
  ConsumerState<LandingScreen> createState() => _LandingScreenState();
}

class _LandingScreenState extends ConsumerState<LandingScreen> {
  late PageController _pageController;

  final List<Map<String, dynamic>> _landingData = [
    {
      "title": "Welcome to\nTravelGator",
      "subtitle": "Your ultimate travel companion for\nexploring the world with confidence\nand staying connected everywhere.",
      "imagePath": "assets/images/splash_background.png",
      "backgroundColor": const Color(0xFFFF6982),
    },
    {
      "title": "Stay Connected\nGlobally",
      "subtitle": "Access reliable internet and\ncommunication services no matter\nwhere your adventures take you.",
      "imagePath": "assets/images/splash_background.png",
      "backgroundColor": const Color(0xFF4A90E2),
    },
    {
      "title": "Discover & Explore\nWith Ease",
      "subtitle": "Find amazing destinations, local\nexperiences, and hidden gems with\nour smart travel recommendations.",
      "imagePath": "assets/images/splash_background.png",
      "backgroundColor": const Color(0xFF7B68EE),
    },
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    ref.read(landingNotifierProvider.notifier).goToPage(page);
  }

  void _onPageTap(int page) {
    _pageController.animateToPage(
      page,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onNextPressed() {
    final currentPage = ref.read(landingNotifierProvider).currentPage;
    if (currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeLanding();
    }
  }

  void _onSkipPressed() {
    _completeLanding();
  }

  void _completeLanding() {
    ref.read(landingNotifierProvider.notifier).completeLanding();
    if (mounted) {
      context.go(AppRoutes.home);
    }
  }

  @override
  Widget build(BuildContext context) {
    final landingState = ref.watch(landingNotifierProvider);
    final currentPage = landingState.currentPage;

    return Scaffold(
      body: Stack(
        children: [
          // PageView for manual scrolling
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: _landingData.length,
            itemBuilder: (context, index) {
              final data = _landingData[index];
              return LandingPage(
                title: data["title"],
                subtitle: data["subtitle"],
                imagePath: data["imagePath"],
                backgroundColor: data["backgroundColor"],
              );
            },
          ),

          // Skip button (top right)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 24,
            child: TextButton(
              onPressed: _onSkipPressed,
              child: const Text(
                'Skip',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          // Bottom navigation area
          Positioned(
            left: 0,
            right: 0,
            bottom: MediaQuery.of(context).padding.bottom + 24,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Page indicator
                  LandingPageIndicator(
                    currentPage: currentPage,
                    totalPages: _landingData.length,
                    onPageTap: _onPageTap,
                  ),
                  const SizedBox(height: 32),
                  
                  // Next/Get Started button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _onNextPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: _landingData[currentPage]["backgroundColor"],
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                      ),
                      child: Text(
                        currentPage == 2 ? 'Get Started' : 'Next',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
