import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/routes/provider/router_state_provider.dart';

/// State for onboarding screen
class OnboardingState {
  final int currentPage;
  final bool isCompleted;

  const OnboardingState({
    this.currentPage = 0,
    this.isCompleted = false,
  });

  OnboardingState copyWith({
    int? currentPage,
    bool? isCompleted,
  }) {
    return OnboardingState(
      currentPage: currentPage ?? this.currentPage,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

/// Notifier for onboarding state management
class OnboardingNotifier extends StateNotifier<OnboardingState> {
  final Ref ref;

  OnboardingNotifier(this.ref) : super(const OnboardingState());

  /// Navigate to a specific page
  void goToPage(int page) {
    if (page >= 0 && page < 3) {
      state = state.copyWith(currentPage: page);
    }
  }

  /// Go to next page
  void nextPage() {
    if (state.currentPage < 2) {
      state = state.copyWith(currentPage: state.currentPage + 1);
    }
  }

  /// Go to previous page
  void previousPage() {
    if (state.currentPage > 0) {
      state = state.copyWith(currentPage: state.currentPage - 1);
    }
  }

  /// Complete onboarding and mark it as done
  Future<void> completeOnboarding() async {
    final routerState = ref.read(routerStateProvider.notifier);
    await routerState.completeOnboarding();
    state = state.copyWith(isCompleted: true);
  }

  /// Skip onboarding
  Future<void> skipOnboarding() async {
    await completeOnboarding();
  }
}

/// Provider for onboarding state
final onboardingNotifierProvider = StateNotifierProvider<OnboardingNotifier, OnboardingState>((ref) {
  return OnboardingNotifier(ref);
});
