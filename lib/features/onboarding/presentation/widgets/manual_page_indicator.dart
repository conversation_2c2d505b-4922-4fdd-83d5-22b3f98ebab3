import 'package:flutter/material.dart';

/// Manual page indicator with dots that can be tapped to navigate
class ManualPageIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Color activeColor;
  final Color inactiveColor;
  final double dotSize;
  final double spacing;
  final Function(int)? onPageTap;

  const ManualPageIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.activeColor = Colors.white,
    this.inactiveColor = Colors.grey,
    this.dotSize = 8.0,
    this.spacing = 12.0,
    this.onPageTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        totalPages,
        (index) => GestureDetector(
          onTap: () => onPageTap?.call(index),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: spacing / 2),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: dotSize,
              width: currentPage == index ? dotSize * 1.8 : dotSize * 0.6,
              decoration: BoxDecoration(
                color: currentPage == index ? activeColor : inactiveColor,
                borderRadius: BorderRadius.circular(dotSize / 2),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
