import 'package:flutter/material.dart';

/// Individual page widget for onboarding carousel
class OnboardingPage extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? imagePath;
  final Color backgroundColor;
  final Color textColor;

  const OnboardingPage({
    super.key,
    required this.title,
    required this.subtitle,
    this.imagePath,
    this.backgroundColor = const Color(0xFFFF6982),
    this.textColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Container(
      width: size.width,
      height: size.height,
      color: backgroundColor,
      child: Stack(
        children: [
          // Background image if provided
          if (imagePath != null)
            Positioned(
              top: 0,
              left: -250,
              height: size.height,
              child: Image.asset(
                imagePath!,
                fit: BoxFit.cover,
                width: size.width + 250,
              ),
            ),
          
          // Gradient overlay
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.transparent, backgroundColor],
                ),
              ),
            ),
          ),
          
          // Content at the bottom
          Positioned(
            left: 0,
            right: 0,
            bottom: 120, // Space for navigation buttons
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: textColor,
                      fontSize: 28,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w700,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: textColor,
                      fontSize: 18,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
