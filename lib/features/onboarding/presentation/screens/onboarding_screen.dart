import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_travelgator/routes/app_router.dart';
import 'package:flutter_travelgator/features/onboarding/presentation/providers/onboarding_state_provider.dart';
import 'package:flutter_travelgator/features/onboarding/presentation/widgets/onboarding_page.dart';
import 'package:flutter_travelgator/features/onboarding/presentation/widgets/manual_page_indicator.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  late PageController _pageController;

  final List<Map<String, dynamic>> _onboardingData = [
    {
      "title": "Stay Connected",
      "subtitle": "No matter where you are, TravelGator\nwill go with you",
      "imagePath": "assets/images/onboarding_background.png",
      "backgroundColor": const Color(0xFFFF6982),
    },
    {
      "title": "Discover Amazing\nDestinations",
      "subtitle": "Find new places, experiences, and\nhidden gems wherever your journey\ntakes you around the globe.",
      "imagePath": "assets/images/onboarding_background.png",
      "backgroundColor": const Color(0xFF4A90E2),
    },
    {
      "title": "Travel Smarter\nWith Confidence",
      "subtitle": "Plan your trips efficiently with\nTravelGator's smart features and\ntravel with complete peace of mind.",
      "imagePath": "assets/images/onboarding_background.png",
      "backgroundColor": const Color(0xFF7B68EE),
    },
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    ref.read(onboardingNotifierProvider.notifier).goToPage(page);
  }

  void _onPageTap(int page) {
    _pageController.animateToPage(
      page,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onNextPressed() {
    final currentPage = ref.read(onboardingNotifierProvider).currentPage;
    if (currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _onSkipPressed() {
    _completeOnboarding();
  }

  void _completeOnboarding() async {
    await ref.read(onboardingNotifierProvider.notifier).completeOnboarding();
    if (mounted) {
      context.go(AppRoutes.home);
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final currentPage = onboardingState.currentPage;

    return Scaffold(
      body: Stack(
        children: [
          // PageView for manual scrolling
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: _onboardingData.length,
            itemBuilder: (context, index) {
              final data = _onboardingData[index];
              return OnboardingPage(
                title: data["title"],
                subtitle: data["subtitle"],
                imagePath: data["imagePath"],
                backgroundColor: data["backgroundColor"],
              );
            },
          ),

          // Skip button (top right)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 24,
            child: TextButton(
              onPressed: _onSkipPressed,
              child: const Text(
                'Skip',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          // Bottom navigation area
          Positioned(
            left: 24,
            right: 24,
            bottom: MediaQuery.of(context).padding.bottom + 32,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Page indicator
                ManualPageIndicator(
                  currentPage: currentPage,
                  totalPages: _onboardingData.length,
                  activeColor: Colors.white,
                  inactiveColor: Colors.white.withValues(alpha: 0.5),
                  onPageTap: _onPageTap,
                ),

                // Next/Get Started button
                ElevatedButton(
                  onPressed: _onNextPressed,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: _onboardingData[currentPage]["backgroundColor"],
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text(
                    currentPage == 2 ? 'Get Started' : 'Next',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
