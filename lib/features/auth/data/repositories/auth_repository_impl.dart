import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_data_source.dart';

/// Implementation of [AuthRepository] that orchestrates data sources
class AuthRepositoryImpl implements AuthRepository {
  final AuthDataSource dataSource;

  AuthRepositoryImpl({required this.dataSource});

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      debugPrint('[AuthRepository] Getting current user from data source...');
      final user = await dataSource.getCurrentUser();

      if (user != null) {
        debugPrint('[AuthRepository] User retrieved: ${user.email}');
        debugPrint('[AuthRepository] Firebase token present: ${user.token != null}');
      } else {
        debugPrint('[AuthRepository] No user found in data source');
      }

      return Right(user);
    } on AuthException catch (e) {
      debugPrint('[AuthRepository] AuthException getting current user: ${e.message}');
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      debugPrint('[AuthRepository] Unexpected error getting current user: $e');
      return Left(AuthFailure(message: e.toString()));
    }
  }

  @override
  Future<bool> isSignedIn() async {
    try {
      debugPrint('[AuthRepository] Checking if user is signed in...');
      final user = await dataSource.getCurrentUser();
      final isSignedIn = user != null;
      debugPrint('[AuthRepository] User signed in: $isSignedIn');
      return isSignedIn;
    } catch (e) {
      debugPrint('[AuthRepository] Error checking sign-in status: $e');
      return false;
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await dataSource.signOut();
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  // Method for Google sign-in
  @override
  Future<Either<Failure, User>> signInWithGoogle() async {
    try {
      final user = await dataSource.signInWithGoogle();
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }

  // Method for Apple sign-in
  @override
  Future<Either<Failure, User>> signInWithApple() async {
    try {
      final user = await dataSource.signInWithApple();
      return Right(user);
    } on AuthException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(AuthFailure(message: e.toString()));
    }
  }
}
