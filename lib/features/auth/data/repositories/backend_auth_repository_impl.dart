import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/network/backend_auth_service.dart';
import '../../domain/entities/backend_auth_result.dart';
import '../../domain/repositories/backend_auth_repository.dart';
import '../datasources/backend_auth_data_source.dart';

/// Implementation of [BackendAuthRepository]
class BackendAuthRepositoryImpl implements BackendAuthRepository {
  final BackendAuthDataSource dataSource;

  BackendAuthRepositoryImpl({required this.dataSource});

  @override
  Future<Either<Failure, BackendAuthResult>> authenticateWithBackend(
    String firebaseIdToken,
  ) async {
    try {
      debugPrint('[BackendAuthRepository] Starting backend authentication...');
      
      final response = await dataSource.authenticate(firebaseIdToken);
      
      debugPrint('[BackendAuthRepository] Backend authentication completed');
      debugPrint('[BackendAuthRepository] Success: ${response.isSuccess}');
      
      return Right(response.toDomain());
    } on BackendAuthException catch (e) {
      debugPrint('[BackendAuthRepository] Backend auth exception: $e');
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      debugPrint('[BackendAuthRepository] Unexpected error: $e');
      return Left(NetworkFailure(message: 'Backend authentication failed: $e'));
    }
  }

  @override
  Future<Either<Failure, BackendAuthResult>> refreshBackendToken(
    String currentBackendToken,
  ) async {
    try {
      final response = await dataSource.refreshToken(currentBackendToken);
      return Right(response.toDomain());
    } on BackendAuthException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(NetworkFailure(message: 'Token refresh failed: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateBackendToken(String backendToken) async {
    try {
      final isValid = await dataSource.validateToken(backendToken);
      return Right(isValid);
    } on BackendAuthException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(NetworkFailure(message: 'Token validation failed: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> signOutFromBackend(String? backendToken) async {
    try {
      await dataSource.signOut(backendToken);
      return const Right(null);
    } on BackendAuthException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(NetworkFailure(message: 'Backend sign out failed: $e'));
    }
  }
}
