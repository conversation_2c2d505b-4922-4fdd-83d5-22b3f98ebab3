import 'package:flutter/foundation.dart';
import '../../../../core/network/backend_auth_service.dart';
import '../models/backend_auth_response_model.dart';

/// Abstract interface for backend authentication data source
abstract class BackendAuthDataSource {
  Future<BackendAuthResponseModel> authenticate(String firebaseIdToken);
  Future<BackendAuthResponseModel> refreshToken(String currentBackendToken);
  Future<bool> validateToken(String backendToken);
  Future<void> signOut(String? backendToken);
}

/// Implementation of backend authentication data source
class BackendAuthDataSourceImpl implements BackendAuthDataSource {
  final BackendAuthService _backendAuthService;

  BackendAuthDataSourceImpl({
    BackendAuthService? backendAuthService,
  }) : _backendAuthService = backendAuthService ?? BackendAuthService();

  @override
  Future<BackendAuthResponseModel> authenticate(String firebaseIdToken) async {
    try {
      debugPrint('[BackendAuthDataSource] Authenticating with backend...');
      
      final response = await _backendAuthService.authenticate(firebaseIdToken);
      
      return BackendAuthResponseModel.fromBackendResponse(response);
    } on BackendAuthException catch (e) {
      debugPrint('[BackendAuthDataSource] Backend auth exception: $e');
      rethrow;
    } catch (e) {
      debugPrint('[BackendAuthDataSource] Unexpected error: $e');
      throw BackendAuthException(
        'Unexpected error during backend authentication: $e',
        originalError: e,
      );
    }
  }

  @override
  Future<BackendAuthResponseModel> refreshToken(String currentBackendToken) async {
    // TODO: Implement token refresh when backend supports it
    throw UnimplementedError('Token refresh not yet implemented');
  }

  @override
  Future<bool> validateToken(String backendToken) async {
    // TODO: Implement token validation when backend supports it
    throw UnimplementedError('Token validation not yet implemented');
  }

  @override
  Future<void> signOut(String? backendToken) async {
    // TODO: Implement backend sign out when backend supports it
    debugPrint('[BackendAuthDataSource] Backend sign out not yet implemented');
  }
}
