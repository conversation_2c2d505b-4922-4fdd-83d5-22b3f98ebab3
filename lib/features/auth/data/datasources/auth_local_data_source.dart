import 'package:flutter_travelgator/core/storage/local_storage.dart';
import 'package:flutter_travelgator/features/auth/data/models/user_model.dart';
import 'package:flutter_travelgator/features/auth/data/models/enhanced_user_model.dart';

/// Local data source for authentication operations
abstract class AuthLocalDataSource {
  /// Cache the user data
  Future<void> cacheUser(UserModel user);

  /// Cache enhanced user data with backend authentication
  Future<void> cacheEnhancedUser(EnhancedUserModel user);

  /// Get the cached user
  Future<UserModel?> getCachedUser();

  /// Get the cached enhanced user (with backend auth data)
  Future<EnhancedUserModel?> getCachedEnhancedUser();

  /// Clear the cached user data
  Future<void> clearUser();

  /// Check if a user is cached
  Future<bool> hasUser();

  /// Check if an enhanced user is cached
  Future<bool> hasEnhancedUser();
}

/// Implementation of [AuthLocalDataSource] with shared preferences
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final LocalStorage localStorage;
  static const String userKey = 'cached_user';
  static const String enhancedUserKey = 'cached_enhanced_user';

  AuthLocalDataSourceImpl({required this.localStorage});

  @override
  Future<void> cacheUser(UserModel user) async {
    await localStorage.set(userKey, user.toJson());
  }

  @override
  Future<void> cacheEnhancedUser(EnhancedUserModel user) async {
    await localStorage.set(enhancedUserKey, user.toJson());
  }

  @override
  Future<UserModel?> getCachedUser() async {
    final jsonData = await localStorage.get<Map<String, dynamic>>(userKey);
    if (jsonData == null) return null;

    try {
      return UserModel.fromJson(jsonData);
    } catch (e) {
      // Handle invalid data format
      await clearUser();
      return null;
    }
  }

  @override
  Future<EnhancedUserModel?> getCachedEnhancedUser() async {
    final jsonData = await localStorage.get<Map<String, dynamic>>(enhancedUserKey);
    if (jsonData == null) return null;

    try {
      return EnhancedUserModel.fromJson(jsonData);
    } catch (e) {
      // Handle invalid data format
      await localStorage.remove(enhancedUserKey);
      return null;
    }
  }

  @override
  Future<void> clearUser() async {
    await Future.wait([
      localStorage.remove(userKey),
      localStorage.remove(enhancedUserKey),
    ]);
  }

  @override
  Future<bool> hasUser() async {
    return await localStorage.containsKey(userKey);
  }

  @override
  Future<bool> hasEnhancedUser() async {
    return await localStorage.containsKey(enhancedUserKey);
  }
}
