import '../models/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;
import '../../../../core/utils/token_debug_helper.dart';
import '../../../../core/utils/apple_debug_helper.dart';

abstract class AuthDataSource {
  Future<void> signOut();
  Future<UserModel?> getCurrentUser();
  Future<UserModel> signInWithGoogle();
  Future<UserModel> signInWithApple();
}

class AuthDataSourceImpl implements AuthDataSource {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;

  AuthDataSourceImpl({
    firebase_auth.FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  }) : _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
       _googleSignIn =
           googleSignIn ??
           GoogleSignIn(
             clientId:
                 Platform.isIOS || Platform.isMacOS
                     ? '448909230583-n412h8gb3nfdepgcotvmo0pcq3fssm62.apps.googleusercontent.com'
                     : null,
             scopes: ['email', 'profile'],
           );

  @override
  Future<void> signOut() async {
    try {
      await Future.wait([_firebaseAuth.signOut(), _googleSignIn.signOut()]);
    } catch (e) {
      debugPrint('Error during sign out: $e');
      rethrow;
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;

      if (firebaseUser == null) {
        return null;
      }

      // Create and return a UserModel from the Firebase user
      return UserModel(
        id: firebaseUser.uid,
        email: firebaseUser.email ?? '',
        name: firebaseUser.displayName ?? 'User',
        profilePhoto: firebaseUser.photoURL,
        isEmailVerified: firebaseUser.emailVerified,
        token: await firebaseUser.getIdToken(),
      );
    } catch (e) {
      debugPrint('Error getting current user: $e');
      return null;
    }
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign-In process...');

      // Trigger the Google Sign-In flow
      debugPrint('Calling GoogleSignIn.signIn()...');
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        debugPrint('Google Sign-In was canceled by the user');
        throw Exception('Google Sign-In was canceled by the user');
      }

      debugPrint('GoogleSignIn successful: ${googleUser.email}');

      // Obtain the auth details from the Google Sign-In
      debugPrint('Getting authentication tokens...');
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      debugPrint(
        'Got tokens - accessToken: ${googleAuth.accessToken != null ? 'present' : 'missing'}, idToken: ${googleAuth.idToken != null ? 'present' : 'missing'}',
      );

      // Debug: Print actual token values using helper
      TokenDebugHelper.debugGoogleTokens(googleAuth);

      // Create a new credential for Firebase
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint(
        'Created Firebase credential, attempting to sign in with credential...',
      );

      // Sign in to Firebase with the Google credential
      final firebase_auth.UserCredential userCredential = await _firebaseAuth
          .signInWithCredential(credential);

      final firebase_auth.User? firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        debugPrint('Firebase user is null after credential sign-in');
        throw Exception('Failed to sign in with Google');
      }

      debugPrint('Firebase auth successful: ${firebaseUser.email}');

      // Get Firebase ID token
      final firebaseIdToken = await firebaseUser.getIdToken();
      debugPrint('Firebase ID Token: $firebaseIdToken');

      // Debug Firebase token using helper
      await TokenDebugHelper.debugFirebaseToken(firebaseUser);

      // Ensure we have a valid Firebase ID token
      if (firebaseIdToken == null) {
        throw Exception('Failed to get Firebase ID token');
      }

      // Create UserModel from Firebase user (backend auth will be handled by composite use case)
      final userModel = UserModel(
        id: firebaseUser.uid,
        email: firebaseUser.email ?? '',
        name: firebaseUser.displayName ?? 'User',
        profilePhoto: firebaseUser.photoURL,
        isEmailVerified: firebaseUser.emailVerified,
        token: firebaseIdToken,
      );

      debugPrint('✅ Google Sign-In completed successfully (Firebase only)');
      debugPrint('Backend authentication will be handled by composite use case if available');

      return userModel;
    } catch (e) {
      // Log error and rethrow for proper error handling
      debugPrint('Google Sign-In error: $e');
      throw Exception('Failed to sign in with Google: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> signInWithApple() async {
    try {
      debugPrint('Starting Apple Sign-In process...');

      // Check if Apple Sign-In is available
      await AppleDebugHelper.debugAppleEnvironment();
      final isAvailable = await SignInWithApple.isAvailable();

      if (!isAvailable) {
        debugPrint('Apple Sign-In is not available on this device');
        throw Exception('Apple Sign-In is not available on this device. This feature requires iOS 13+ and proper Apple Developer configuration.');
      }

      // Trigger the Apple Sign-In flow with comprehensive debugging
      debugPrint('Apple Sign-In is available, proceeding...');
      final appleCredential = await AppleDebugHelper.debugAppleSignInFlow(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      ).catchError((error, stackTrace) {
        AppleDebugHelper.debugAppleError(error, stackTrace);
        throw error;
      });

      debugPrint('Apple Sign-In successful');

      // Debug: Print Apple credential info
      debugPrint(
        'Got Apple tokens - identityToken: present, authorizationCode: present',
      );

      // Create a new credential for Firebase
      final oauthCredential = firebase_auth.OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      debugPrint(
        'Created Firebase credential, attempting to sign in with credential...',
      );

      // Sign in to Firebase with the Apple credential
      final firebase_auth.UserCredential userCredential = await _firebaseAuth
          .signInWithCredential(oauthCredential);

      final firebase_auth.User? firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        debugPrint('Firebase user is null after credential sign-in');
        throw Exception('Failed to sign in with Apple');
      }

      debugPrint('Firebase auth successful: ${firebaseUser.email}');

      // Get Firebase ID token
      final firebaseIdToken = await firebaseUser.getIdToken();
      debugPrint('Firebase ID Token: $firebaseIdToken');

      // Debug Firebase token using helper
      await TokenDebugHelper.debugFirebaseToken(firebaseUser);

      // Ensure we have a valid Firebase ID token
      if (firebaseIdToken == null) {
        throw Exception('Failed to get Firebase ID token');
      }

      // Create UserModel from Firebase user (backend auth will be handled by composite use case)
      final userModel = UserModel(
        id: firebaseUser.uid,
        email: firebaseUser.email ?? '',
        name: firebaseUser.displayName ??
              '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim(),
        profilePhoto: firebaseUser.photoURL,
        isEmailVerified: firebaseUser.emailVerified,
        token: firebaseIdToken,
      );

      debugPrint('✅ Apple Sign-In completed successfully (Firebase only)');
      debugPrint('Backend authentication will be handled by composite use case if available');

      return userModel;
    } catch (e) {
      // Log error and rethrow for proper error handling
      debugPrint('Apple Sign-In error: $e');
      throw Exception('Failed to sign in with Apple: ${e.toString()}');
    }
  }
}
