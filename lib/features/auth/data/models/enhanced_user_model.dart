import '../../domain/entities/user.dart';
import '../../domain/entities/backend_auth_result.dart';
import '../models/user_model.dart';

/// Enhanced user model that combines Firebase user data with backend authentication
class EnhancedUserModel extends UserModel {
  final BackendAuthResult? backendAuthResult;

  const EnhancedUserModel({
    required super.id,
    required super.name,
    required super.email,
    super.profilePhoto,
    super.token,
    super.isEmailVerified,
    super.backendData,
    super.backendToken,
    this.backendAuthResult,
  });

  /// Create from base User and backend auth result
  factory EnhancedUserModel.fromUserAndBackendAuth({
    required User user,
    BackendAuthResult? backendAuthResult,
  }) {
    // Prioritize backend user data for name if available
    String displayName = user.name;
    if (backendAuthResult?.userData != null) {
      final backendName = backendAuthResult!.userData!['name'] as String?;
      if (backendName != null && backendName.isNotEmpty) {
        displayName = backendName;
      }
    }

    // Fallback to email username if name is still empty
    if (displayName.isEmpty) {
      final emailParts = user.email.split('@');
      if (emailParts.isNotEmpty) {
        displayName = emailParts.first;
      }
    }

    return EnhancedUserModel(
      id: user.id,
      name: displayName,
      email: user.email,
      profilePhoto: user.profilePhoto,
      token: user.token,
      isEmailVerified: user.isEmailVerified,
      backendData: backendAuthResult?.additionalData,
      backendToken: backendAuthResult?.backendToken,
      backendAuthResult: backendAuthResult,
    );
  }

  /// Create from UserModel and backend auth result
  factory EnhancedUserModel.fromUserModelAndBackendAuth({
    required UserModel userModel,
    BackendAuthResult? backendAuthResult,
  }) {
    // Prioritize backend user data for name if available
    String displayName = userModel.name;
    if (backendAuthResult?.userData != null) {
      final backendName = backendAuthResult!.userData!['name'] as String?;
      if (backendName != null && backendName.isNotEmpty) {
        displayName = backendName;
      }
    }

    // Fallback to email username if name is still empty
    if (displayName.isEmpty) {
      final emailParts = userModel.email.split('@');
      if (emailParts.isNotEmpty) {
        displayName = emailParts.first;
      }
    }

    return EnhancedUserModel(
      id: userModel.id,
      name: displayName,
      email: userModel.email,
      profilePhoto: userModel.profilePhoto,
      token: userModel.token,
      isEmailVerified: userModel.isEmailVerified,
      backendData: backendAuthResult?.additionalData ?? userModel.backendData,
      backendToken: backendAuthResult?.backendToken ?? userModel.backendToken,
      backendAuthResult: backendAuthResult,
    );
  }

  /// Check if backend authentication was successful
  bool get hasBackendAuth => 
      backendAuthResult != null && backendAuthResult!.isSuccess;

  /// Get backend user data if available
  Map<String, dynamic>? get backendUserData => backendAuthResult?.userData;

  /// Create a copy with updated backend auth result
  EnhancedUserModel copyWithBackendAuthResult(BackendAuthResult? backendAuthResult) {
    return EnhancedUserModel(
      id: id,
      name: name,
      email: email,
      profilePhoto: profilePhoto,
      token: token,
      isEmailVerified: isEmailVerified,
      backendData: backendAuthResult?.additionalData ?? backendData,
      backendToken: backendAuthResult?.backendToken ?? backendToken,
      backendAuthResult: backendAuthResult,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json['backend_auth_result'] = backendAuthResult != null
        ? {
            'is_success': backendAuthResult!.isSuccess,
            'message': backendAuthResult!.message,
            'backend_token': backendAuthResult!.backendToken,
            'user_data': backendAuthResult!.userData,
            'additional_data': backendAuthResult!.additionalData,
          }
        : null;
    return json;
  }

  /// Create from JSON with backend auth result
  factory EnhancedUserModel.fromJson(Map<String, dynamic> json) {
    BackendAuthResult? backendAuthResult;
    
    if (json['backend_auth_result'] != null) {
      final backendJson = json['backend_auth_result'] as Map<String, dynamic>;
      backendAuthResult = BackendAuthResult(
        isSuccess: backendJson['is_success'] ?? false,
        message: backendJson['message'],
        backendToken: backendJson['backend_token'],
        userData: backendJson['user_data'] as Map<String, dynamic>?,
        additionalData: backendJson['additional_data'] as Map<String, dynamic>?,
      );
    }

    return EnhancedUserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      profilePhoto: json['profile_photo'] as String?,
      token: json['token'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      backendData: json['backend_data'] as Map<String, dynamic>?,
      backendToken: json['backend_token'] as String?,
      backendAuthResult: backendAuthResult,
    );
  }
}
