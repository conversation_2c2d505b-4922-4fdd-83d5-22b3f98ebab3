import '../../../../core/network/backend_auth_service.dart';
import '../../domain/entities/backend_auth_result.dart';

/// Data model for backend authentication response
class BackendAuthResponseModel extends BackendAuthResult {
  const BackendAuthResponseModel({
    required super.isSuccess,
    super.message,
    super.backendToken,
    super.userData,
    super.additionalData,
  });

  /// Create from backend service response
  factory BackendAuthResponseModel.fromBackendResponse(
    BackendAuthResponse response,
  ) {
    return BackendAuthResponseModel(
      isSuccess: response.success,
      message: response.message,
      backendToken: response.backendToken,
      userData: response.user,
      additionalData: response.data,
    );
  }

  /// Create from JSON
  factory BackendAuthResponseModel.fromJson(Map<String, dynamic> json) {
    return BackendAuthResponseModel(
      isSuccess: json['is_success'] ?? false,
      message: json['message'],
      backendToken: json['backend_token'],
      userData: json['user_data'] as Map<String, dynamic>?,
      additionalData: json['additional_data'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'is_success': isSuccess,
      'message': message,
      'backend_token': backendToken,
      'user_data': userData,
      'additional_data': additionalData,
    };
  }

  /// Convert to domain entity
  BackendAuthResult toDomain() {
    return BackendAuthResult(
      isSuccess: isSuccess,
      message: message,
      backendToken: backendToken,
      userData: userData,
      additionalData: additionalData,
    );
  }
}
