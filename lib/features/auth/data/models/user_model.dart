import 'package:flutter_travelgator/features/auth/domain/entities/user.dart';

/// Data model for User entity
class UserModel extends User {
  /// Backend authentication response data
  final Map<String, dynamic>? backendData;

  /// Backend authentication token (different from Firebase token)
  final String? backendToken;

  /// Create a new UserModel instance
  const UserModel({
    required super.id,
    required super.name,
    required super.email,
    super.profilePhoto,
    super.token,
    super.isEmailVerified,
    this.backendData,
    this.backendToken,
  });

  /// Create a UserModel from a JSON map
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      profilePhoto: json['profile_photo'] as String?,
      token: json['token'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      backendData: json['backend_data'] as Map<String, dynamic>?,
      backendToken: json['backend_token'] as String?,
    );
  }

  /// Convert this UserModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profile_photo': profilePhoto,
      'token': token,
      'is_email_verified': isEmailVerified,
      'backend_data': backendData,
      'backend_token': backendToken,
    };
  }

  /// Convert a User entity to a UserModel
  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      name: user.name,
      email: user.email,
      profilePhoto: user.profilePhoto,
      token: user.token,
      isEmailVerified: user.isEmailVerified,
      // Backend data will be null when converting from base User entity
      backendData: null,
      backendToken: null,
    );
  }

  /// Create a copy of this UserModel with backend authentication data
  UserModel copyWithBackendAuth({
    Map<String, dynamic>? backendData,
    String? backendToken,
  }) {
    return UserModel(
      id: id,
      name: name,
      email: email,
      profilePhoto: profilePhoto,
      token: token,
      isEmailVerified: isEmailVerified,
      backendData: backendData ?? this.backendData,
      backendToken: backendToken ?? this.backendToken,
    );
  }
}
