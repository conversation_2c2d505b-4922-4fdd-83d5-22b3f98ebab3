// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backend_auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$backendAuthServiceHash() =>
    r'f8cd79fec36f37942112d071569ab61742021ec4';

/// See also [backendAuthService].
@ProviderFor(backendAuthService)
final backendAuthServiceProvider =
    AutoDisposeProvider<BackendAuthService>.internal(
      backendAuthService,
      name: r'backendAuthServiceProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$backendAuthServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BackendAuthServiceRef = AutoDisposeProviderRef<BackendAuthService>;
String _$backendAuthDataSourceHash() =>
    r'b434f31f9036fdee45d6ebef57c06f57568dccfe';

/// See also [backendAuthDataSource].
@ProviderFor(backendAuthDataSource)
final backendAuthDataSourceProvider =
    AutoDisposeProvider<BackendAuthDataSource>.internal(
      backendAuthDataSource,
      name: r'backendAuthDataSourceProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$backendAuthDataSourceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BackendAuthDataSourceRef =
    AutoDisposeProviderRef<BackendAuthDataSource>;
String _$backendAuthRepositoryHash() =>
    r'a9e8147051c59d263dff1d58c8aab37fa55f06ff';

/// See also [backendAuthRepository].
@ProviderFor(backendAuthRepository)
final backendAuthRepositoryProvider =
    AutoDisposeProvider<BackendAuthRepository>.internal(
      backendAuthRepository,
      name: r'backendAuthRepositoryProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$backendAuthRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BackendAuthRepositoryRef =
    AutoDisposeProviderRef<BackendAuthRepository>;
String _$authenticateWithBackendUseCaseHash() =>
    r'a50501db266e63af942cd89f92b915fed4070859';

/// See also [authenticateWithBackendUseCase].
@ProviderFor(authenticateWithBackendUseCase)
final authenticateWithBackendUseCaseProvider =
    AutoDisposeProvider<AuthenticateWithBackendUseCase?>.internal(
      authenticateWithBackendUseCase,
      name: r'authenticateWithBackendUseCaseProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$authenticateWithBackendUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthenticateWithBackendUseCaseRef =
    AutoDisposeProviderRef<AuthenticateWithBackendUseCase?>;
String _$compositeGoogleSignInUseCaseHash() =>
    r'80b01f3f4535842963ee0f6030f8b4a483261f86';

/// See also [compositeGoogleSignInUseCase].
@ProviderFor(compositeGoogleSignInUseCase)
final compositeGoogleSignInUseCaseProvider =
    AutoDisposeProvider<CompositeGoogleSignInUseCase?>.internal(
      compositeGoogleSignInUseCase,
      name: r'compositeGoogleSignInUseCaseProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$compositeGoogleSignInUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CompositeGoogleSignInUseCaseRef =
    AutoDisposeProviderRef<CompositeGoogleSignInUseCase?>;
String _$compositeAppleSignInUseCaseHash() =>
    r'2ce54386373311a14b8fcd58383006164f737c9f';

/// See also [compositeAppleSignInUseCase].
@ProviderFor(compositeAppleSignInUseCase)
final compositeAppleSignInUseCaseProvider =
    AutoDisposeProvider<CompositeAppleSignInUseCase?>.internal(
      compositeAppleSignInUseCase,
      name: r'compositeAppleSignInUseCaseProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$compositeAppleSignInUseCaseHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CompositeAppleSignInUseCaseRef =
    AutoDisposeProviderRef<CompositeAppleSignInUseCase?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
