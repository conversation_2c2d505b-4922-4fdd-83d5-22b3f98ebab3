import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../main.dart';
import '../../data/datasources/auth_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/usecases/apple_sign_in_use_case.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/sign_in_use_case.dart';
import '../../domain/usecases/sign_up_use_case.dart';
import '../../domain/usecases/google_sign_in_use_case.dart';
import '../../domain/usecases/composite_google_sign_in_use_case.dart';
import '../../domain/usecases/composite_apple_sign_in_use_case.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import './auth_state.dart';

import '../../data/models/enhanced_user_model.dart';
import '../../data/datasources/auth_local_data_source.dart';
import '../../../../core/storage/local_storage.dart';
import 'backend_auth_providers.dart';

part 'auth_providers.g.dart';

// Skip the type parameter completely as per Riverpod 2.0+ convention
// Data sources
@riverpod
AuthDataSource? authDataSource(authDataSourceRef) {
  final isFirebaseInitialized = authDataSourceRef.watch(firebaseInitProvider);

  if (!isFirebaseInitialized) {
    // Firebase isn't initialized, return null or show a message
    debugPrint('Firebase is not initialized, auth features will be disabled');
    return null;
  }

  try {
    return AuthDataSourceImpl();
  } catch (e) {
    debugPrint('Error creating AuthDataSourceImpl: $e');
    return null;
  }
}

@riverpod
LocalStorage localStorage(localStorageRef) {
  return LocalStorageImpl();
}

@riverpod
AuthLocalDataSource authLocalDataSource(authLocalDataSourceRef) {
  final localStorage = authLocalDataSourceRef.watch(localStorageProvider);
  return AuthLocalDataSourceImpl(localStorage: localStorage);
}

// Repositories
@riverpod
AuthRepository? authRepository(authRepositoryRef) {
  final dataSource = authRepositoryRef.watch(authDataSourceProvider);

  if (dataSource == null) {
    return null;
  }

  return AuthRepositoryImpl(dataSource: dataSource);
}

// Use cases
@riverpod
SignInUseCase? signInUseCase(signInUseCaseRef) {
  final repository = signInUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return SignInUseCase(repository);
}

@riverpod
SignUpUseCase? signUpUseCase(signUpUseCaseRef) {
  final repository = signUpUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return SignUpUseCase(repository);
}

@riverpod
GoogleSignInUseCase? googleSignInUseCase(googleSignInUseCaseRef) {
  final repository = googleSignInUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return GoogleSignInUseCase(repository);
}

@riverpod
AppleSignInUseCase? appleSignInUseCase(appleSignInUseCaseRef) {
  final repository = appleSignInUseCaseRef.watch(authRepositoryProvider);

  if (repository == null) {
    return null;
  }

  return AppleSignInUseCase(repository);
}

// Auth notifier with code generation
@riverpod
class AuthNotifier extends _$AuthNotifier {
  bool _isCheckingAuth = false;

  @override
  AuthState build() {
    // Start with loading state instead of initial to avoid race condition
    _checkAuthStatus();
    return const AuthState.loading();
  }

  /// Initialize the auth state based on stored credentials
  /// This method now includes backend authentication validation on app startup
  Future<void> _checkAuthStatus() async {
    // Prevent multiple concurrent auth checks
    if (_isCheckingAuth) {
      debugPrint('[AuthNotifier] Auth check already in progress, skipping...');
      return;
    }

    _isCheckingAuth = true;

    try {
      // Use safe reads to avoid uninitialized provider errors
      final repository = ref.read(authRepositoryProvider);
      final backendAuthRepository = ref.read(backendAuthRepositoryProvider);
      final localDataSource = ref.read(authLocalDataSourceProvider);

      if (repository == null) {
        state = const AuthState.unauthenticated();
        return;
      }

      // Check current state safely - if we can't read it, assume we need to check auth
      bool isCurrentlyAuthenticated = false;
      try {
        isCurrentlyAuthenticated = state.maybeMap(
          authenticated: (_) => true,
          orElse: () => false,
        );
      } catch (e) {
        debugPrint('[AuthNotifier] Could not read current state, proceeding with auth check: $e');
        isCurrentlyAuthenticated = false;
      }

      if (!isCurrentlyAuthenticated) {
        state = const AuthState.loading();
      }

      debugPrint('[AuthNotifier] Checking authentication status on app startup...');

      // First check if we have a cached enhanced user with backend auth
      final cachedEnhancedUser = await localDataSource.getCachedEnhancedUser();
      if (cachedEnhancedUser != null && cachedEnhancedUser.hasBackendAuth) {
        debugPrint('[AuthNotifier] Found cached enhanced user with backend auth');

        // Validate that Firebase user is still signed in
        final isSignedIn = await repository.isSignedIn();
        if (isSignedIn) {
          debugPrint('[AuthNotifier] Firebase user still signed in, using cached enhanced user');
          state = AuthState.authenticated(cachedEnhancedUser);
          debugPrint('[AuthNotifier] Auth check completed successfully with cached user');
          return;
        } else {
          debugPrint('[AuthNotifier] Firebase user no longer signed in, clearing cache');
          await localDataSource.clearUser();
        }
      }

      // Fallback to Firebase authentication check
      final isSignedIn = await repository.isSignedIn();
      if (!isSignedIn) {
        debugPrint('[AuthNotifier] No Firebase user found, setting unauthenticated');
        state = const AuthState.unauthenticated();
        return;
      }

      debugPrint('[AuthNotifier] Firebase user found, getting user details...');
      final userResult = await repository.getCurrentUser();

      await userResult.fold(
        (failure) async {
          debugPrint('[AuthNotifier] Failed to get current user: ${failure.message}');
          state = const AuthState.unauthenticated();
        },
        (user) async {
          if (user == null) {
            debugPrint('[AuthNotifier] User is null, setting unauthenticated');
            state = const AuthState.unauthenticated();
            return;
          }

          debugPrint('[AuthNotifier] Firebase user retrieved: ${user.email}');

          // Attempt backend authentication with Firebase token
          if (user.token != null && user.token!.isNotEmpty) {
            debugPrint('[AuthNotifier] Attempting backend authentication on startup...');

            final backendResult = await backendAuthRepository.authenticateWithBackend(user.token!);

            await backendResult.fold(
              (failure) async {
                debugPrint('[AuthNotifier] Backend authentication failed on startup: ${failure.message}');
                debugPrint('[AuthNotifier] Treating as authentication failure, signing out...');

                // Backend auth failed, sign out the user completely
                await repository.signOut();
                state = const AuthState.unauthenticated();
              },
              (backendAuthResult) async {
                debugPrint('[AuthNotifier] Backend authentication successful on startup');

                // Create enhanced user model with backend data
                final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
                  user: user,
                  backendAuthResult: backendAuthResult,
                );

                // Cache the enhanced user for future app starts
                await _cacheEnhancedUser(enhancedUser);

                debugPrint('[AuthNotifier] Setting authenticated state with backend data');
                state = AuthState.authenticated(enhancedUser);
              },
            );
          } else {
            debugPrint('[AuthNotifier] No Firebase token available, treating as authentication failure');
            await repository.signOut();
            state = const AuthState.unauthenticated();
          }
        },
      );
    } catch (e) {
      debugPrint('[AuthNotifier] Unexpected error during auth status check: $e');
      state = const AuthState.unauthenticated();
    } finally {
      _isCheckingAuth = false;
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    final repository = ref.read(authRepositoryProvider);
    final localDataSource = ref.read(authLocalDataSourceProvider);

    if (repository == null) {
      state = const AuthState.unauthenticated();
      return;
    }

    state = const AuthState.loading();

    // Clear cached user data
    try {
      await localDataSource.clearUser();
      debugPrint('[AuthNotifier] Cached user data cleared');
    } catch (e) {
      debugPrint('[AuthNotifier] Failed to clear cached user data: $e');
    }

    final result = await repository.signOut();

    state = result.fold(
      (failure) => AuthState.error(failure),
      (_) => const AuthState.unauthenticated(),
    );
  }

  /// Sign in with Google using composite authentication
  Future<void> signInWithGoogle() async {
    // Try to use composite use case first (Firebase + Backend)
    final compositeUseCase = ref.read(compositeGoogleSignInUseCaseProvider);
    await _signInWithCompositeUseCase(compositeUseCase!);

  }

  /// Sign in with Google (Firebase only) - for modal authentication
  Future<void> signInWithGoogleFirebaseOnly() async {
    final useCase = ref.read(googleSignInUseCaseProvider);

    if (useCase == null) {
      debugPrint('Google Sign-In use case is null');
      state = AuthState.error(
        AuthFailure(message: 'Google Sign-In services are not available'),
      );
      return;
    }

    try {
      debugPrint('Starting Firebase-only Google Sign-In');

      final result = await useCase();

      state = result.fold(
        (failure) {
          debugPrint('Google sign-in failed: ${failure.message}');
          return AuthState.error(failure);
        },
        (user) {
          debugPrint('Google sign-in succeeded for user: ${user.email}');
          return AuthState.authenticated(user);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in Firebase-only Google sign-in: $e');
      debugPrint('Stack trace: $stackTrace');
      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Google: ${e.toString()}'),
      );
    }
  }

  /// Sign in using composite use case (Firebase + Backend)
  Future<void> _signInWithCompositeUseCase(CompositeGoogleSignInUseCase useCase) async {
    try {
      debugPrint('Starting composite Google Sign-In (Firebase + Backend)');
      // state = const AuthState.loading();

      final result = await useCase();

      await result.fold(
        (failure) async {
          debugPrint('Composite sign-in failed: ${failure.message}');

          // Clear any cached user data when composite authentication fails
          try {
            final localDataSource = ref.read(authLocalDataSourceProvider);
            await localDataSource.clearUser();
            debugPrint('[AuthNotifier] Cached user data cleared after composite auth failure');
          } catch (e) {
            debugPrint('[AuthNotifier] Failed to clear cached user data: $e');
          }

          state = AuthState.error(failure);
        },
        (compositeResult) async {
          debugPrint('Composite sign-in succeeded for user: ${compositeResult.user.email}');
          debugPrint('Firebase token present: ${compositeResult.user.token != null}');
          debugPrint('Backend auth successful: ${compositeResult.hasBackendAuth}');

          if (compositeResult.hasBackendAuth) {
            debugPrint('Backend token present: ${compositeResult.backendToken.isNotEmpty}');
            debugPrint('Backend user data: ${compositeResult.backendUserData}');
          }

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: compositeResult.user,
            backendAuthResult: compositeResult.backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          state = AuthState.authenticated(enhancedUser);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in composite sign-in: $e');
      debugPrint('Stack trace: $stackTrace');

      // Clear any cached user data when exception occurs
      try {
        final localDataSource = ref.read(authLocalDataSourceProvider);
        await localDataSource.clearUser();
        debugPrint('[AuthNotifier] Cached user data cleared after exception');
      } catch (clearError) {
        debugPrint('[AuthNotifier] Failed to clear cached user data: $clearError');
      }

      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Google: ${e.toString()}'),
      );
    }
  }

  /// Sign in with Apple using composite authentication
  Future<void> signInWithApple() async {
    // Try to use composite use case first (Firebase + Backend)
    final compositeUseCase = ref.read(compositeAppleSignInUseCaseProvider);
    if (compositeUseCase != null) {
      await _signInWithCompositeAppleUseCase(compositeUseCase);
    } else {
      // Fallback to Firebase-only authentication
      await signInWithAppleFirebaseOnly();
    }
  }

  /// Sign in with Apple (Firebase only) - for modal authentication
  Future<void> signInWithAppleFirebaseOnly() async {
    final useCase = ref.read(appleSignInUseCaseProvider);

    if (useCase == null) {
      debugPrint('Apple Sign-In use case is null');
      state = AuthState.error(
        AuthFailure(message: 'Apple Sign-In services are not available'),
      );
      return;
    }

    try {
      debugPrint('Starting Firebase-only Apple Sign-In');

      final result = await useCase();

      state = result.fold(
        (failure) {
          debugPrint('Apple sign-in failed: ${failure.message}');
          return AuthState.error(failure);
        },
        (user) {
          debugPrint('Apple sign-in succeeded for user: ${user.email}');
          return AuthState.authenticated(user);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in Firebase-only Apple sign-in: $e');
      debugPrint('Stack trace: $stackTrace');
      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Apple: ${e.toString()}'),
      );
    }
  }

  /// Sign in using composite Apple use case (Firebase + Backend)
  Future<void> _signInWithCompositeAppleUseCase(CompositeAppleSignInUseCase useCase) async {
    try {
      debugPrint('Starting composite Apple Sign-In (Firebase + Backend)');
      // state = const AuthState.loading();

      final result = await useCase();

      await result.fold(
        (failure) async {
          debugPrint('Composite Apple sign-in failed: ${failure.message}');

          // Clear any cached user data when composite authentication fails
          try {
            final localDataSource = ref.read(authLocalDataSourceProvider);
            await localDataSource.clearUser();
            debugPrint('[AuthNotifier] Cached user data cleared after composite Apple auth failure');
          } catch (e) {
            debugPrint('[AuthNotifier] Failed to clear cached user data: $e');
          }

          state = AuthState.error(failure);
        },
        (compositeResult) async {
          debugPrint('Composite Apple sign-in succeeded for user: ${compositeResult.user.email}');
          debugPrint('Firebase token present: ${compositeResult.user.token != null}');
          debugPrint('Backend auth successful: ${compositeResult.hasBackendAuth}');

          if (compositeResult.hasBackendAuth) {
            debugPrint('Backend token present: ${compositeResult.backendToken.isNotEmpty}');
            debugPrint('Backend user data: ${compositeResult.backendUserData}');
          }

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: compositeResult.user,
            backendAuthResult: compositeResult.backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          state = AuthState.authenticated(enhancedUser);
        },
      );
    } catch (e, stackTrace) {
      debugPrint('Exception in composite Apple sign-in: $e');
      debugPrint('Stack trace: $stackTrace');

      // Clear any cached user data when exception occurs
      try {
        final localDataSource = ref.read(authLocalDataSourceProvider);
        await localDataSource.clearUser();
        debugPrint('[AuthNotifier] Cached user data cleared after Apple auth exception');
      } catch (clearError) {
        debugPrint('[AuthNotifier] Failed to clear cached user data: $clearError');
      }

      state = AuthState.error(
        AuthFailure(message: 'Failed to sign in with Apple: ${e.toString()}'),
      );
    }
  }

  /// Authenticate with backend using current Firebase token
  Future<bool> authenticateWithBackend() async {
    final currentState = state;

    // Check if user is authenticated with Firebase and get user
    final user = currentState.user;
    if (user == null) {
      debugPrint('[AuthNotifier] Cannot authenticate with backend: user not authenticated with Firebase');
      return false;
    }

    final firebaseToken = user.token;

    if (firebaseToken == null || firebaseToken.isEmpty) {
      debugPrint('[AuthNotifier] Cannot authenticate with backend: no Firebase token available');
      return false;
    }

    try {
      debugPrint('[AuthNotifier] Starting backend authentication...');

      final backendAuthRepository = ref.read(backendAuthRepositoryProvider);
      final backendResult = await backendAuthRepository.authenticateWithBackend(firebaseToken);

      return await backendResult.fold(
        (failure) async {
          debugPrint('[AuthNotifier] Backend authentication failed: ${failure.message}');
          return false;
        },
        (backendAuthResult) async {
          debugPrint('[AuthNotifier] Backend authentication successful');

          // Create enhanced user model with backend data
          final enhancedUser = EnhancedUserModel.fromUserAndBackendAuth(
            user: user,
            backendAuthResult: backendAuthResult,
          );

          // Cache the enhanced user for future app starts
          await _cacheEnhancedUser(enhancedUser);

          // Update state with enhanced user
          state = AuthState.authenticated(enhancedUser);

          return true;
        },
      );
    } catch (e) {
      debugPrint('[AuthNotifier] Exception during backend authentication: $e');
      return false;
    }
  }

  /// Cache enhanced user data for persistence across app restarts
  Future<void> _cacheEnhancedUser(EnhancedUserModel user) async {
    try {
      final localDataSource = ref.read(authLocalDataSourceProvider);
      await localDataSource.cacheEnhancedUser(user);
      debugPrint('[AuthNotifier] Enhanced user cached successfully');
    } catch (e) {
      debugPrint('[AuthNotifier] Failed to cache enhanced user: $e');
      // Don't throw error, just log it as caching is not critical
    }
  }
}
