import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/auth_repository.dart';

/// Provider for auth repository
/// Note: This is a legacy provider. Use authRepositoryProvider from auth_providers.dart instead
final authRepositoryProvider = Provider<AuthRepository?>((ref) {
  // This provider is deprecated. Use the one from auth_providers.dart
  return null;
});

/// Provider for auth state
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  if (repository == null) {
    throw Exception('Auth repository not initialized');
  }
  return AuthNotifier(repository);
});

/// Auth state class
class AuthState {
  final bool isAuthenticated;
  final String? userId;
  final String? error;

  const AuthState({
    this.isAuthenticated = false,
    this.userId,
    this.error,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    String? userId,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      userId: userId ?? this.userId,
      error: error,
    );
  }
}

/// Notifier for auth operations
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _repository;

  AuthNotifier(this._repository) : super(const AuthState());

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    final result = await _repository.signInWithGoogle();
    result.fold(
      (failure) => state = state.copyWith(
        error: failure.message,
      ),
      (user) => state = state.copyWith(
        isAuthenticated: true,
        userId: user.id,
        error: null,
      ),
    );
  }

  /// Sign out
  Future<void> signOut() async {
    final result = await _repository.signOut();
    result.fold(
      (failure) => state = state.copyWith(
        error: failure.message,
      ),
      (_) => state = const AuthState(),
    );
  }

  /// Get current user
  Future<void> getCurrentUser() async {
    final result = await _repository.getCurrentUser();
    result.fold(
      (failure) => state = state.copyWith(
        error: failure.message,
      ),
      (user) => state = state.copyWith(
        isAuthenticated: user != null,
        userId: user?.id,
        error: null,
      ),
    );
  }
}