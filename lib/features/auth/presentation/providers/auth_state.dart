import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_travelgator/features/auth/domain/entities/user.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';

part 'auth_state.freezed.dart';

/// Represents the authentication state
@Freezed(unionKey: 'type')
class AuthState with _$AuthState {
  /// User is not authenticated and UI is in initial state
  const factory AuthState.initial() = _Initial;

  /// Authentication in progress
  const factory AuthState.loading() = _Loading;

  /// User is authenticated
  const factory AuthState.authenticated(User user) = _Authenticated;

  /// User is not authenticated
  const factory AuthState.unauthenticated() = _Unauthenticated;

  /// Authentication failed with error
  const factory AuthState.error(Failure failure) = _Error;

  // Add these extension methods until code generation is fixed
  const AuthState._(); // Private constructor for the extension methods

  // Getter for user property - returns null if not authenticated state
  User? get user {
    return this is _Authenticated ? (this as _Authenticated).user : null;
  }

  // Getter for failure property - returns null if not error state
  Failure? get failure {
    return this is _Error ? (this as _Error).failure : null;
  }

  // Manual implementation of maybeMap method
  T maybeMap<T>({
    T Function(AuthState)? initial,
    T Function(AuthState)? loading,
    T Function(AuthState)? authenticated,
    T Function(AuthState)? unauthenticated,
    T Function(AuthState)? error,
    required T Function() orElse,
  }) {
    if (this is _Initial && initial != null) {
      return initial(this);
    }
    if (this is _Loading && loading != null) {
      return loading(this);
    }
    if (this is _Authenticated && authenticated != null) {
      return authenticated(this);
    }
    if (this is _Unauthenticated && unauthenticated != null) {
      return unauthenticated(this);
    }
    if (this is _Error && error != null) {
      return error(this);
    }
    return orElse();
  }

  // Manual implementation of map method
  T map<T>({
    required T Function(AuthState) initial,
    required T Function(AuthState) loading,
    required T Function(AuthState) authenticated,
    required T Function(AuthState) unauthenticated,
    required T Function(AuthState) error,
  }) {
    if (this is _Initial) {
      return initial(this);
    }
    if (this is _Loading) {
      return loading(this);
    }
    if (this is _Authenticated) {
      return authenticated(this);
    }
    if (this is _Unauthenticated) {
      return unauthenticated(this);
    }
    if (this is _Error) {
      return error(this);
    }
    throw Exception('Unsupported AuthState type');
  }

  // Manual implementation of when method
  T when<T>({
    required T Function() initial,
    required T Function() loading,
    required T Function(User user) authenticated,
    required T Function() unauthenticated,
    required T Function(Failure failure) error,
  }) {
    if (this is _Initial) {
      return initial();
    }
    if (this is _Loading) {
      return loading();
    }
    if (this is _Authenticated) {
      return authenticated((this as _Authenticated).user);
    }
    if (this is _Unauthenticated) {
      return unauthenticated();
    }
    if (this is _Error) {
      return error((this as _Error).failure);
    }
    throw Exception('Unsupported AuthState type');
  }

  // Manual implementation of maybeWhen method
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? loading,
    T Function(User user)? authenticated,
    T Function()? unauthenticated,
    T Function(Failure failure)? error,
    required T Function() orElse,
  }) {
    if (this is _Initial && initial != null) {
      return initial();
    }
    if (this is _Loading && loading != null) {
      return loading();
    }
    if (this is _Authenticated && authenticated != null) {
      return authenticated((this as _Authenticated).user);
    }
    if (this is _Unauthenticated && unauthenticated != null) {
      return unauthenticated();
    }
    if (this is _Error && error != null) {
      return error((this as _Error).failure);
    }
    return orElse();
  }
}
