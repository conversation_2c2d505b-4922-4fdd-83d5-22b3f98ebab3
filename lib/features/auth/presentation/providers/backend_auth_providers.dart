import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/network/backend_auth_service.dart';
import '../../data/datasources/backend_auth_data_source.dart';
import '../../data/repositories/backend_auth_repository_impl.dart';
import '../../domain/repositories/backend_auth_repository.dart';
import '../../domain/usecases/authenticate_with_backend_use_case.dart';
import '../../domain/usecases/composite_google_sign_in_use_case.dart';
import '../../domain/usecases/composite_apple_sign_in_use_case.dart';
import 'auth_providers.dart';

part 'backend_auth_providers.g.dart';

// Core backend auth service
@riverpod
BackendAuthService backendAuthService(Ref ref) {
  return BackendAuthService();
}

// Backend auth data source
@riverpod
BackendAuthDataSource backendAuthDataSource(Ref ref) {
  final service = ref.watch(backendAuthServiceProvider);
  return BackendAuthDataSourceImpl(backendAuthService: service);
}

// Backend auth repository
@riverpod
BackendAuthRepository backendAuthRepository(Ref ref) {
  final dataSource = ref.watch(backendAuthDataSourceProvider);
  return BackendAuthRepositoryImpl(dataSource: dataSource);
}

// Backend authentication use case
@riverpod
AuthenticateWithBackendUseCase? authenticateWithBackendUseCase(
  Ref ref,
) {
  final repository = ref.watch(backendAuthRepositoryProvider);
  return AuthenticateWithBackendUseCase(repository);
}

// Composite Google sign-in use case (combines Firebase + Backend)
@riverpod
CompositeGoogleSignInUseCase? compositeGoogleSignInUseCase(
  Ref ref,
) {
  final authRepository = ref.watch(authRepositoryProvider);
  final backendAuthRepository = ref.watch(backendAuthRepositoryProvider);

  if (authRepository == null) {
    debugPrint('Auth repository not available for composite use case');
    return null;
  }

  return CompositeGoogleSignInUseCase(
    authRepository: authRepository,
    backendAuthRepository: backendAuthRepository,
  );
}

// Composite Apple sign-in use case (combines Firebase + Backend)
@riverpod
CompositeAppleSignInUseCase? compositeAppleSignInUseCase(
  Ref ref,
) {
  final authRepository = ref.watch(authRepositoryProvider);
  final backendAuthRepository = ref.watch(backendAuthRepositoryProvider);

  if (authRepository == null) {
    debugPrint('Auth repository not available for composite Apple use case');
    return null;
  }

  return CompositeAppleSignInUseCase(
    authRepository: authRepository,
    backendAuthRepository: backendAuthRepository,
  );
}
