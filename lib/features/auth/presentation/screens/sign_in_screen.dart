// COMMENTED OUT FOR MODAL AUTHENTICATION IMPLEMENTATION
// This screen is kept for future reference but not currently used
// Authentication is now handled via modal popups

/*
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_travelgator/l10n/app_localizations.dart';
import '../providers/auth_providers.dart';
import '../providers/auth_state.dart';
import '../widgets/index.dart';
import '../mixins/auth_redirect_mixin.dart';
import '../../../../routes/app_router.dart';

class SignInScreen extends ConsumerStatefulWidget {
  const SignInScreen({super.key});

  @override
  ConsumerState<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends ConsumerState<SignInScreen>
    with AuthRedirectMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _isGoogleLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);
    final l10n = AppLocalizations.of(context);

    // Use the mixin for auth redirect
    setupAuthRedirect();

    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            const AuthHeader(),
            _buildSignInForm(context, authState, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildSignInForm(
    BuildContext context,
    AuthState authState,
    AppLocalizations? l10n,
  ) {
    final primaryColor = Color(0xFFFF6982);
    final isLoading = authState.maybeMap(
      loading: (_) => true,
      orElse: () => false,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              l10n?.signIn ?? 'Sign in',
              textScaler: const TextScaler.linear(1.0), // Prevent text scaling
              style: GoogleFonts.inter(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF111827),
              ),
            ),
            const SizedBox(height: 24),

            // Email input
            AuthTextField(
              controller: _emailController,
              hint: 'Your Email',
              prefixIcon: SvgIcons.profileIcon(
                color: const Color(0xFF9CA3AF),
                width: 20,
                height: 20,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email';
                }
                if (!value.contains('@')) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),

            // Password input
            AuthTextField(
              controller: _passwordController,
              hint: 'Password',
              obscureText: _obscurePassword,
              prefixIcon: SvgIcons.passwordIcon(
                color: const Color(0xFF9CA3AF),
                width: 24,
                height: 24,
              ),
              suffixIcon: IconButton(
                icon: SvgIcons.eyeIcon(
                  color: const Color(0xFF9CA3AF),
                  width: 24,
                  height: 24,
                  isVisible: !_obscurePassword,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a password';
                }
                if (value.length < 6) {
                  return 'Password must be at least 6 characters';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Forgot password
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  'Forgot password',
                  textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: primaryColor,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Sign in button
            AuthButton(
              label: 'Sign In',
              onPressed: () {
                if (_formKey.currentState?.validate() ?? false) {
                  // TODO: Implement email/password sign in
                  debugPrint('Sign in with email: ${_emailController.text}');
                }
              },
              isLoading: isLoading,
            ),

            const SizedBox(height: 24),

            // Don't have an account
            Center(
              child: TextButton(
                onPressed: () {
                  context.go(AppRoutes.signUp);
                },
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: RichText(
                  textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                  text: TextSpan(
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    children: [
                      TextSpan(
                        text: 'Dont have an account? ',
                        style: TextStyle(color: const Color(0xFF1F2937)),
                      ),
                      TextSpan(
                        text: 'SIGN UP',
                        style: TextStyle(color: primaryColor),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Divider
            const AuthDivider(),

            const SizedBox(height: 24),

            // Social login options
            SocialLoginOptions(
              onGoogleTap: () async {
                try {
                  setState(() {
                    _isGoogleLoading = true;
                  });

                  // Use the AuthNotifier to handle the sign-in
                  final authNotifier = ref.read(authNotifierProvider.notifier);
                  await authNotifier.signInWithGoogle();

                  // No need to manually navigate - the AuthRedirectMixin will handle this
                  // based on the auth state changes
                } catch (e) {
                  // Handle any errors
                  debugPrint('Google sign-in error: $e');
                } finally {
                  if (mounted) {
                    setState(() {
                      _isGoogleLoading = false;
                    });
                  }
                }
              },
              onAppleTap: () {
                // Apple auth not implemented yet
              },
              isLoading: _isGoogleLoading,
            ),
          ],
        ),
      ),
    );
  }
}
*/
