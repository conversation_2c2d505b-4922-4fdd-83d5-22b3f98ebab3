import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_guard_service.dart';
import '../providers/auth_providers.dart';

/// Test screen for manually testing the authentication modal
/// This can be used during development to test the modal functionality
class AuthModalTestScreen extends ConsumerWidget {
  const AuthModalTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Auth Modal Test'),
        backgroundColor: const Color(0xFFFF6982),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Current auth state
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Authentication State:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      authState.maybeMap(
                        initial: (_) => 'Initial (not authenticated)',
                        loading: (_) => 'Loading...',
                        authenticated: (state) {
                          final user = state.user;
                          return 'Authenticated: ${user?.email ?? 'No email'}';
                        },
                        unauthenticated: (_) => 'Unauthenticated',
                        orElse: () => 'Unknown state',
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Test buttons
            const Text(
              'Test Authentication Modal:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // Test cart authentication
            ElevatedButton(
              onPressed: () => _testCartAuth(context, ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Test Cart Authentication'),
            ),

            const SizedBox(height: 12),

            // Test purchase authentication
            ElevatedButton(
              onPressed: () => _testPurchaseAuth(context, ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Test Purchase Authentication'),
            ),

            const SizedBox(height: 12),

            // Test account authentication
            ElevatedButton(
              onPressed: () => _testAccountAuth(context, ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Test Account Authentication'),
            ),

            const SizedBox(height: 12),

            // Test review purchase authentication
            ElevatedButton(
              onPressed: () => _testReviewPurchaseAuth(context, ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6982),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Test Review Purchase Authentication'),
            ),

            const SizedBox(height: 12),

            // Test generic authentication
            ElevatedButton(
              onPressed: () => _testGenericAuth(context, ref),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Test Generic Authentication'),
            ),

            const SizedBox(height: 24),

            // Logout button (if authenticated)
            authState.maybeMap(
              authenticated: (_) => ElevatedButton(
                onPressed: () => _logout(ref),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Logout'),
              ),
              orElse: () => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testCartAuth(BuildContext context, WidgetRef ref) async {
    final result = await AuthGuardService.requireAuthForCart(
      context,
      ref,
      onSuccess: () {
        if (context.mounted) {
          _showSuccessSnackBar(context, 'Cart authentication successful!');
        }
      },
    );

    if (result && context.mounted) {
      _showSuccessSnackBar(context, 'Already authenticated for cart');
    }
  }

  Future<void> _testPurchaseAuth(BuildContext context, WidgetRef ref) async {
    final result = await AuthGuardService.requireAuthForPurchase(
      context,
      ref,
      onSuccess: () {
        if (context.mounted) {
          _showSuccessSnackBar(context, 'Purchase authentication successful!');
        }
      },
    );

    if (result && context.mounted) {
      _showSuccessSnackBar(context, 'Already authenticated for purchase');
    }
  }

  Future<void> _testAccountAuth(BuildContext context, WidgetRef ref) async {
    final result = await AuthGuardService.requireAuthForAccount(
      context,
      ref,
      onSuccess: () {
        if (context.mounted) {
          _showSuccessSnackBar(context, 'Account authentication successful!');
        }
      },
    );

    if (result && context.mounted) {
      _showSuccessSnackBar(context, 'Already authenticated for account');
    }
  }

  Future<void> _testReviewPurchaseAuth(BuildContext context, WidgetRef ref) async {
    final result = await AuthGuardService.requireAuthForReviewPurchase(
      context,
      ref,
      onSuccess: () {
        if (context.mounted) {
          _showSuccessSnackBar(context, 'Review purchase authentication successful!');
        }
      },
    );

    if (result && context.mounted) {
      _showSuccessSnackBar(context, 'Already authenticated for review purchase');
    }
  }

  Future<void> _testGenericAuth(BuildContext context, WidgetRef ref) async {
    await AuthGuardService.showAuthModal(
      context,
      title: 'Custom Test Modal',
      subtitle: 'This is a test of the authentication modal',
      onSuccess: () {
        _showSuccessSnackBar(context, 'Generic authentication successful!');
      },
    );
  }

  void _logout(WidgetRef ref) {
    ref.read(authNotifierProvider.notifier).signOut();
  }

  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}
