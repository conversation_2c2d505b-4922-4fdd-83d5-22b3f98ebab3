// COMMENTED OUT FOR MODAL AUTHENTICATION IMPLEMENTATION
// This screen is kept for future reference but not currently used
// Authentication is now handled via modal popups

/*
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_travelgator/l10n/app_localizations.dart';
import '../providers/auth_providers.dart';
import '../providers/auth_state.dart';
import '../widgets/index.dart';
import '../mixins/auth_redirect_mixin.dart';
import '../../../../routes/app_router.dart';

class SignUpScreen extends ConsumerStatefulWidget {
  const SignUpScreen({super.key});

  @override
  ConsumerState<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends ConsumerState<SignUpScreen>
    with AuthRedirectMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _referralCodeController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _referralCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);
    final l10n = AppLocalizations.of(context);

    // Use the mixin for auth redirect
    setupAuthRedirect();

    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            const AuthHeader(),
            _buildSignUpForm(context, authState, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildSignUpForm(
    BuildContext context,
    AuthState authState,
    AppLocalizations? l10n,
  ) {
    final primaryColor = Color(0xFFFF6982);
    final isLoading = authState.maybeMap(
      loading: (_) => true,
      orElse: () => false,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'Create new account',
              textScaler: const TextScaler.linear(1.0), // Prevent text scaling
              style: GoogleFonts.inter(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF111827),
              ),
            ),
            const SizedBox(height: 24),

            // Email input
            SimpleTextField(
              controller: _emailController,
              hint: 'Email',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email';
                }
                if (!value.contains('@')) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),

            // Password input
            SimpleTextField(
              controller: _passwordController,
              hint: 'Password',
              obscureText: _obscurePassword,
              suffixIcon: IconButton(
                icon: SvgIcons.eyeIcon(
                  color: const Color(0xFF9CA3AF),
                  width: 24,
                  height: 24,
                  isVisible: !_obscurePassword,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a password';
                }
                if (value.length < 6) {
                  return 'Password must be at least 6 characters';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),

            // Confirm Password input
            SimpleTextField(
              controller: _confirmPasswordController,
              hint: 'Confirm Password',
              obscureText: _obscureConfirmPassword,
              suffixIcon: IconButton(
                icon: SvgIcons.eyeIcon(
                  color: const Color(0xFF9CA3AF),
                  width: 24,
                  height: 24,
                  isVisible: !_obscureConfirmPassword,
                ),
                onPressed: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please confirm your password';
                }
                if (value != _passwordController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),

            // Referral Code input (Optional)
            SimpleTextField(
              controller: _referralCodeController,
              hint: 'Referral Code (Optional)',
              validator: null, // Optional field
            ),
            const SizedBox(height: 24),

            // Terms and conditions text
            SizedBox(
              width: 343,
              height: 50,
              child: Align(
                alignment: Alignment.centerLeft,
                child: RichText(
                  textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                  text: TextSpan(
                    style: GoogleFonts.inter(
                      fontSize: 13.5,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF1F2937),
                    ),
                    children: [
                      const TextSpan(
                        text: 'By tapping "Sign Up" you agree to our ',
                      ),
                      TextSpan(
                        text: 'Terms',
                        style: const TextStyle(
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      const TextSpan(
                        text: '. Learn how we process your data in our ',
                      ),
                      TextSpan(
                        text: 'Privacy Policy',
                        style: const TextStyle(
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Sign up button
            AuthButton(
              label: 'Sign Up',
              onPressed: () {
                if (_formKey.currentState?.validate() ?? false) {
                  // TODO: Implement email/password sign up
                  debugPrint('Sign up with email: ${_emailController.text}');
                }
              },
              isLoading: isLoading,
            ),

            const SizedBox(height: 24),

            // Already have an account
            Center(
              child: TextButton(
                onPressed: () => context.go(AppRoutes.signIn),
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: RichText(
                  textScaler: const TextScaler.linear(1.0), // Prevent text scaling
                  text: TextSpan(
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    children: [
                      TextSpan(
                        text: 'Already have an account? ',
                        style: TextStyle(color: const Color(0xFF1F2937)),
                      ),
                      TextSpan(
                        text: 'SIGN IN',
                        style: TextStyle(color: primaryColor),
                      ),
                    ],
                  ),
                ),
              ),
            ),


          ],
        ),
      ),
    );
  }
}
*/
