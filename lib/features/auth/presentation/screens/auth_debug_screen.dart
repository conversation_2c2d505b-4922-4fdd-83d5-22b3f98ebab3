import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/backend_auth_debug_widget.dart';
import '../providers/auth_providers.dart';
import '../../data/models/user_model.dart';
import '../../../../core/utils/token_debug_helper.dart';

/// Debug screen to test and view authentication status
class AuthDebugScreen extends ConsumerWidget {
  const AuthDebugScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentication Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Backend authentication status widget
            const BackendAuthDebugWidget(),
            
            // Additional debug information
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Authentication Actions',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Sign in with Google button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: authState.maybeWhen(
                          loading: () => null,
                          orElse: () => () async {
                            final authNotifier = ref.read(authNotifierProvider.notifier);
                            await authNotifier.signInWithGoogle();
                          },
                        ),
                        child: authState.maybeWhen(
                          loading: () => const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          orElse: () => const Text('Sign In with Google'),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Sign out button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: authState.maybeWhen(
                          authenticated: (_) => () async {
                            final authNotifier = ref.read(authNotifierProvider.notifier);
                            await authNotifier.signOut();
                          },
                          orElse: () => null,
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Sign Out'),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Debug all tokens button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () async {
                          await TokenDebugHelper.debugAllTokens();
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Check debug console for token information'),
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Debug All Tokens'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Current user information
            authState.when(
              authenticated: (user) => Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Current User Information',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('ID: ${user.id}'),
                      Text('Name: ${user.name}'),
                      Text('Email: ${user.email}'),
                      Text('Email Verified: ${user.isEmailVerified}'),
                      if (user.profilePhoto != null)
                        Text('Profile Photo: ${user.profilePhoto}'),
                      if (user is UserModel && user.backendToken != null)
                        Text('Backend Token Length: ${user.backendToken!.length}'),
                    ],
                  ),
                ),
              ),
              loading: () => const Card(
                margin: EdgeInsets.all(16),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(child: CircularProgressIndicator()),
                ),
              ),
              unauthenticated: () => const Card(
                margin: EdgeInsets.all(16),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('No user authenticated'),
                ),
              ),
              error: (failure) => Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Authentication Error',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        failure.message,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ),
              initial: () => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }
}
