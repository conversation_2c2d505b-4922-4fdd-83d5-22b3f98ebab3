import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_guard_service.dart';

// Temporary placeholder screens to maintain routing compatibility
// These will redirect to the authentication modal

class SignInScreen extends ConsumerWidget {
  const SignInScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Don't automatically redirect - this was causing modal dismissal issues
    // Just show the auth modal without navigation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('[TempSignInScreen] Showing auth modal without navigation redirect');
      AuthGuardService.showAuthModal(context);
    });

    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class SignUpScreen extends ConsumerWidget {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Redirect to home and show auth modal
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.go('/home');
      AuthGuardService.showAuthModal(context);
    });

    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
