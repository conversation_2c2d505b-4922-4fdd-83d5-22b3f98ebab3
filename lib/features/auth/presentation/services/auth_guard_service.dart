import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_providers.dart';
import '../widgets/auth_modal.dart';
import '../../data/models/enhanced_user_model.dart';

/// Service for handling authentication guards and modal presentation
class AuthGuardService {
  static final AuthGuardService _instance = AuthGuardService._internal();
  factory AuthGuardService() => _instance;
  AuthGuardService._internal();

  /// Check if user is authenticated and show modal if not
  /// Returns true if user is authenticated, false if modal was shown
  static Future<bool> requireAuthentication(
    BuildContext context,
    WidgetRef ref, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
  }) async {
    final authState = ref.read(authNotifierProvider);

    final isAuthenticated = authState.maybeMap(
      authenticated: (_) => true,
      orElse: () => false,
    );

    if (isAuthenticated) {
      return true;
    }

    // Show authentication modal
    await showAuthModal(
      context,
      title: title,
      subtitle: subtitle,
      onSuccess: onSuccess,
    );

    return false;
  }

  /// Check if user has backend authentication and show modal if not
  /// Returns true if user has backend auth, false if modal was shown
  static Future<bool> requireBackendAuthentication(
    BuildContext context,
    WidgetRef ref, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireBackendAuthentication called');
    final authState = ref.read(authNotifierProvider);
    debugPrint('[AuthGuard] Current auth state type: ${authState.runtimeType}');

    final hasBackendAuth = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;

          // Debug logging to understand the issue
          debugPrint('[AuthGuard] Enhanced user details:');
          debugPrint('[AuthGuard] - hasBackendAuth: ${enhancedUser.hasBackendAuth}');
          debugPrint('[AuthGuard] - backendToken: ${enhancedUser.backendToken}');
          debugPrint('[AuthGuard] - backendToken != null: ${enhancedUser.backendToken != null}');
          debugPrint('[AuthGuard] - backendToken.isNotEmpty: ${enhancedUser.backendToken?.isNotEmpty ?? false}');
          debugPrint('[AuthGuard] - backendAuthResult: ${enhancedUser.backendAuthResult}');
          debugPrint('[AuthGuard] - backendAuthResult.isSuccess: ${enhancedUser.backendAuthResult?.isSuccess}');
          debugPrint('[AuthGuard] - backendAuthResult.backendToken: ${enhancedUser.backendAuthResult?.backendToken}');

          // For now, just check hasBackendAuth since it validates isSuccess
          // The backendToken check might be too strict if backend doesn't return token
          final hasAuth = enhancedUser.hasBackendAuth;
          debugPrint('[AuthGuard] Final hasAuth result (simplified): $hasAuth');
          return hasAuth;
        }
        debugPrint('[AuthGuard] User is not EnhancedUserModel');
        return false;
      },
      orElse: () {
        debugPrint('[AuthGuard] User is not authenticated');
        return false;
      },
    );

    if (hasBackendAuth) {
      debugPrint('[AuthGuard] User already has backend auth, calling success callback');
      // User is already authenticated, call success callback
      onSuccess?.call();
      return true;
    }

    // Double-check by watching the provider (in case of race condition)
    final watchedAuthState = ref.watch(authNotifierProvider);
    final doubleCheckHasAuth = watchedAuthState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth;
        }
        return false;
      },
      orElse: () => false,
    );

    if (doubleCheckHasAuth) {
      debugPrint('[AuthGuard] Double-check confirmed user has backend auth');
      onSuccess?.call();
      return true;
    }

    debugPrint('[AuthGuard] Showing authentication modal');
    // Show authentication modal
    await showAuthModal(
      context,
      title: title,
      subtitle: subtitle,
      onSuccess: onSuccess,
      onError: onError,
    );

    return false;
  }

  /// Show the authentication modal with slide-up animation
  static Future<void> showAuthModal(
    BuildContext context, {
    String? title,
    String? subtitle,
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] Showing authentication modal');
    debugPrint('[AuthGuard] Current context: ${context.runtimeType}');

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      useSafeArea: false,
      builder: (context) {
        return AuthModal(
          title: title,
          subtitle: subtitle,
          onSuccess: onSuccess,
          onError: onError,
        );
      },
    );

    debugPrint('[AuthGuard] Authentication modal dialog completed');
    debugPrint('[AuthGuard] Returning to previous screen - parent modal should still be open');
  }

  /// Check authentication for cart operations
  static Future<bool> requireAuthForCart(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForCart called');
    return requireBackendAuthentication(
      context,
      ref,
      title: 'Sign in to access cart',
      subtitle: 'Create an account or sign in to save items to your cart',
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  /// Check authentication for purchase operations
  static Future<bool> requireAuthForPurchase(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
    Function(String)? onError,
  }) async {
    debugPrint('[AuthGuard] requireAuthForPurchase called');
    return requireBackendAuthentication(
      context,
      ref,
      title: 'Sign in to purchase',
      subtitle: 'Create an account or sign in to complete your purchase',
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  /// Check authentication for review purchase flow
  static Future<bool> requireAuthForReviewPurchase(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
  }) async {
    return requireAuthentication(
      context,
      ref,
      title: 'Sign in to review purchase',
      subtitle: 'Create an account or sign in to review and complete your order',
      onSuccess: onSuccess,
    );
  }

  /// Check authentication for account access
  static Future<bool> requireAuthForAccount(
    BuildContext context,
    WidgetRef ref, {
    VoidCallback? onSuccess,
  }) async {
    return requireAuthentication(
      context,
      ref,
      title: 'Sign in to your account',
      subtitle: 'Access your profile, orders, and preferences',
      onSuccess: onSuccess,
    );
  }
}
