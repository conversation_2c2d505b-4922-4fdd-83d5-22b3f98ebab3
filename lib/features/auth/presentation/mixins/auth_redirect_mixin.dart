import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../routes/app_router.dart';
import '../providers/auth_providers.dart';
import '../providers/auth_state.dart';

/// A mixin for handling authentication redirects
mixin AuthRedirectMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  bool _hasSetupListener = false;

  /// Sets up a listener for authentication state changes to redirect accordingly
  /// This should be called from the build method, not initState
  void setupAuthRedirect() {
    if (_hasSetupListener) return;

    ref.listen<AuthState>(authNotifierProvider, (previous, current) {
      current.maybeMap(
        authenticated: (state) {
          // If on auth screens, redirect to home
          final location = GoRouterState.of(context).fullPath;
          debugPrint('[AuthRedirectMixin] Authentication successful, current location: $location');

          // Only redirect if we're actually on dedicated auth screens, not modals
          if (location == AppRoutes.signIn || location == AppRoutes.signUp) {
            debugPrint('[AuthRedirectMixin] Redirecting from auth screen to home');
            context.go(AppRoutes.home);
          } else {
            debugPrint('[AuthRedirectMixin] Not redirecting - not on dedicated auth screen');
          }
        },
        unauthenticated: (_) {
          // If on protected routes, redirect to home
          final location = GoRouterState.of(context).fullPath;
          debugPrint('[AuthRedirectMixin] User unauthenticated, current location: $location');

          // Check if we're on account routes (protected routes)
          if (location?.startsWith('/account') == true) {
            debugPrint('[AuthRedirectMixin] Redirecting from account route to home after logout');
            context.go(AppRoutes.home);
          } else if (location != AppRoutes.signIn && location != AppRoutes.signUp && location != AppRoutes.home) {
            // For other protected routes (not auth screens or home), redirect to sign in
            debugPrint('[AuthRedirectMixin] Redirecting to sign in');
            context.go(AppRoutes.signIn);
          }
        },
        loading: (_) {
          // Do nothing during loading to prevent unwanted redirects
          debugPrint('[AuthRedirectMixin] Authentication loading state');
        },
        orElse: () {},
      );
    });

    _hasSetupListener = true;
  }
}
