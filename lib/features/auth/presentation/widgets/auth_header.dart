import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../../../../routes/app_router.dart';

class AuthHeader extends StatelessWidget {
  const AuthHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      margin: const EdgeInsets.only(top: 41),
      height: 56,
      child: Row(
        children: [
          _buildLogo(),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFF6B7280), size: 28),
            iconSize: 28,
            onPressed: () {
              context.go(AppRoutes.home);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLogo() {
    return SizedBox(
      height: 24,
      child: SvgPicture.asset(
        'assets/images/travelgator_logo.svg',
        height: 24,
        fit: BoxFit.contain,
      ),
    );
  }
}
