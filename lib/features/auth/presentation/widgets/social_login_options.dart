import 'package:flutter/material.dart';
import 'social_button.dart';
import 'svg_icons.dart';

class SocialLoginOptions extends StatelessWidget {
  final VoidCallback onGoogleTap;
  final VoidCallback onAppleTap;
  final bool isLoading;

  const SocialLoginOptions({
    required this.onGoogleTap,
    required this.onAppleTap,
    this.isLoading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _googleButton(onTap: onGoogleTap, isLoading: isLoading),
        const SizedBox(width: 16),
        _appleButton(onTap: onAppleTap, isLoading: false),
      ],
    );
  }

  Widget _googleButton({required VoidCallback onTap, required bool isLoading}) {
    return SocialButtonContainer(
      onTap: onTap,
      isLoading: isLoading,
      child: SvgIcons.googleLogo(
        width: 50,
        height: 50,
      ),
    );
  }

  Widget _appleButton({required VoidCallback onTap, required bool isLoading}) {
    return SocialButtonContainer(
      onTap: onTap,
      isLoading: isLoading,
      child: SvgIcons.appleLogo(
        color: const Color(0xFF6B7280),
        width: 50,
        height: 50,
      ),
    );
  }
}
