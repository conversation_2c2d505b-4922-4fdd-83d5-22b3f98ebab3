import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../providers/auth_providers.dart';
import '../../data/models/enhanced_user_model.dart';

class AuthModal extends ConsumerStatefulWidget {
  final String? title;
  final String? subtitle;
  final VoidCallback? onSuccess;
  final Function(String)? onError;

  const AuthModal({
    super.key,
    this.title,
    this.subtitle,
    this.onSuccess,
    this.onError,
  });

  @override
  ConsumerState<AuthModal> createState() => _AuthModalState();
}

class _AuthModalState extends ConsumerState<AuthModal>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  bool _isGoogleLoading = false;
  bool _isAppleLoading = false;
  bool _isClosing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _closeModal() async {
    if (_isClosing) return; // Prevent multiple calls
    _isClosing = true;

    try {
      await _animationController.reverse();
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('[AuthModal] Error closing modal: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _closeModal,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withValues(alpha: 0.8 * _fadeAnimation.value),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: GestureDetector(
                  onTap: () {}, // Prevent tap from propagating to background
                  child: Transform.translate(
                    offset: Offset(0, MediaQuery.of(context).size.height * _slideAnimation.value),
              child: Container(
                width: double.infinity,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -10),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    // Handle bar
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: const Color(0xFFE5E7EB),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Content
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(height: 16),

                            // Title
                            Text(
                              widget.title ?? 'Create an account to continue',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.inter(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF111827),
                                height: 1.2,
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Terms text
                            RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: const Color(0xFF6B7280),
                                  height: 1.4,
                                ),
                                children: [
                                  const TextSpan(text: 'By continuing, you agree to our '),
                                  TextSpan(
                                    text: 'User Agreement',
                                    style: GoogleFonts.inter(
                                      decoration: TextDecoration.underline,
                                      color: const Color(0xFF111827),
                                    ),
                                  ),
                                  const TextSpan(text: ' and\nacknowledge that you understand the '),
                                  TextSpan(
                                    text: 'Privacy Policy',
                                    style: GoogleFonts.inter(
                                      decoration: TextDecoration.underline,
                                      color: const Color(0xFF111827),
                                    ),
                                  ),
                                  const TextSpan(text: '.'),
                                ],
                              ),
                            ),

                            const SizedBox(height: 32),

                            // Google button
                            _buildAuthButton(
                              icon: null,
                              text: 'Continue with Google',
                              onTap: _handleGoogleSignIn,
                              isLoading: _isGoogleLoading,
                              isGoogle: true,
                            ),

                            const SizedBox(height: 12),

                            // Apple button
                            _buildAuthButton(
                              icon: Icons.apple,
                              text: 'Continue with Apple',
                              onTap: _handleAppleSignIn,
                              isLoading: _isAppleLoading,
                            ),

                            const SizedBox(height: 32),

                            // Already have account text
                            RichText(
                              text: TextSpan(
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: const Color(0xFF6B7280),
                                ),
                                children: [
                                  const TextSpan(text: 'Already have an account? '),
                                  TextSpan(
                                    text: 'Log in',
                                    style: GoogleFonts.inter(
                                      color: const Color(0xFF3B82F6),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ),
                  ],
                  ),
                  ),
                ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleGoogleSignIn() async {
    try {
      setState(() {
        _isGoogleLoading = true;
      });

      final authNotifier = ref.read(authNotifierProvider.notifier);

      // Use composite authentication (Firebase + Backend) instead of Firebase-only
      await authNotifier.signInWithGoogle();

      // Check if composite authentication was successful
      final authState = ref.read(authNotifierProvider);
      debugPrint('Google sign-in completed. Auth state: $authState');

      await authState.maybeMap(
        authenticated: (state) async {
          debugPrint('Google composite authentication successful! User: ${state.user?.email ?? 'No email'}');

          // Check if this is an enhanced user with backend auth
          if (state.user is EnhancedUserModel) {
            final enhancedUser = state.user as EnhancedUserModel;
            if (enhancedUser.hasBackendAuth) {
              debugPrint('Backend authentication already included in composite auth');
              if (mounted) {
                SnackbarUtils.showAuthSuccess(context, customMessage: 'Successfully signed in!');
                _closeModal();
                widget.onSuccess?.call();
              }
              return;
            }
          }

          // If we reach here, it means we only have Firebase auth, need backend auth
          debugPrint('Only Firebase auth available, attempting backend authentication...');
          final backendSuccess = await authNotifier.authenticateWithBackend();

          if (mounted) {
            if (backendSuccess) {
              SnackbarUtils.showAuthSuccess(context, customMessage: 'Successfully signed in!');
              _closeModal();
              widget.onSuccess?.call();
            } else {
              SnackbarUtils.showAuthError(context, 'Backend authentication failed');
              widget.onError?.call('Backend authentication failed');
            }
          }
        },
        error: (errorState) async {
          debugPrint('Google sign-in error state: ${errorState.failure?.message ?? 'Unknown error'}');
          final errorMessage = errorState.failure?.message ?? 'Unknown error';
          if (mounted) {
            SnackbarUtils.showAuthError(context, errorMessage);
            widget.onError?.call(errorMessage);
          }
        },
        orElse: () async {
          // Handle error if needed
          debugPrint('Google sign-in did not result in authenticated state. Current state: $authState');
          const errorMessage = 'Authentication failed';
          if (mounted) {
            SnackbarUtils.showAuthError(context, errorMessage);
            widget.onError?.call(errorMessage);
          }
        },
      );
    } catch (e) {
      debugPrint('Google sign-in error: $e');
      if (mounted) {
        final errorMessage = e.toString();
        SnackbarUtils.showAuthError(context, errorMessage);
        widget.onError?.call(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGoogleLoading = false;
        });
      }
    }
  }

  Future<void> _handleAppleSignIn() async {
    try {
      setState(() {
        _isAppleLoading = true;
      });

      final authNotifier = ref.read(authNotifierProvider.notifier);

      // Use composite authentication (Firebase + Backend) instead of Firebase-only
      await authNotifier.signInWithApple();

      // Check if composite authentication was successful
      final authState = ref.read(authNotifierProvider);
      debugPrint('Apple sign-in completed. Auth state: $authState');

      await authState.maybeMap(
        authenticated: (state) async {
          debugPrint('Apple composite authentication successful! User: ${state.user?.email ?? 'No email'}');

          // Check if this is an enhanced user with backend auth
          if (state.user is EnhancedUserModel) {
            final enhancedUser = state.user as EnhancedUserModel;
            if (enhancedUser.hasBackendAuth) {
              debugPrint('Backend authentication already included in composite auth');
              if (mounted) {
                SnackbarUtils.showAuthSuccess(context, customMessage: 'Successfully signed in!');
                _closeModal();
                widget.onSuccess?.call();
              }
              return;
            }
          }

          // If we reach here, it means we only have Firebase auth, need backend auth
          debugPrint('Only Firebase auth available, attempting backend authentication...');
          final backendSuccess = await authNotifier.authenticateWithBackend();

          if (mounted) {
            if (backendSuccess) {
              SnackbarUtils.showAuthSuccess(context, customMessage: 'Successfully signed in!');
              _closeModal();
              widget.onSuccess?.call();
            } else {
              SnackbarUtils.showAuthError(context, 'Backend authentication failed');
              widget.onError?.call('Backend authentication failed');
            }
          }
        },
        error: (errorState) async {
          debugPrint('Apple sign-in error state: ${errorState.failure?.message ?? 'Unknown error'}');
          final errorMessage = errorState.failure?.message ?? 'Unknown error';
          if (mounted) {
            SnackbarUtils.showAuthError(context, errorMessage);
            widget.onError?.call(errorMessage);
          }
        },
        orElse: () async {
          // Handle error if needed
          debugPrint('Apple sign-in did not result in authenticated state. Current state: $authState');
          const errorMessage = 'Authentication failed';
          if (mounted) {
            SnackbarUtils.showAuthError(context, errorMessage);
            widget.onError?.call(errorMessage);
          }
        },
      );
    } catch (e) {
      debugPrint('Apple sign-in error: $e');
      if (mounted) {
        final errorMessage = e.toString();
        SnackbarUtils.showAuthError(context, errorMessage);
        widget.onError?.call(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAppleLoading = false;
        });
      }
    }
  }



  Widget _buildAuthButton({
    required String text,
    required VoidCallback onTap,
    required bool isLoading,
    IconData? icon,
    bool isGoogle = false,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: const Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const SizedBox(width: 16),
            // Icon container with fixed size for consistent positioning
            SizedBox(
              width: 50 ,
              height: 50,
              child: Center(
                child: isGoogle
                    ? SvgPicture.asset(
                        'assets/icons/google_logo.svg',
                        width: 36,
                        height: 36,
                      )
                    : SvgPicture.asset(
                        'assets/icons/apple_logo.svg',
                        width: 40,
                        height: 40,
                      ),
              ),
            ),
            Expanded(
              child: Center(
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF111827)),
                        ),
                      )
                    : Text(
                        text,
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF111827),
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 40), // Balance the left padding + icon
          ],
        ),
      ),
    );
  }
}
