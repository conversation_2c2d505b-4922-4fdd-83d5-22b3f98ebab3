import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// SVG icons that match the exact Figma design
class SvgIcons {
  /// Profile icon - matches Iconly/Light-outline/Profile from Figma
  static Widget profileIcon({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      'assets/icons/profile_icon.svg',
      width: width ?? 20,
      height: height ?? 20,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Password/Lock icon - matches Iconly/Light-outline/Password from Figma
  static Widget passwordIcon({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      'assets/icons/password_icon.svg',
      width: width ?? 24,
      height: height ?? 24,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Eye/Show icon - matches Iconly/Light-outline/Show from Figma
  static Widget showIcon({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      'assets/icons/show_icon.svg',
      width: width ?? 24,
      height: height ?? 24,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Google logo - matches LogoSocial / GG from Figma
  static Widget googleLogo({
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      'assets/icons/google_logo.svg',
      width: width ?? 40,
      height: height ?? 40,
      fit: BoxFit.contain,
    );
  }

  /// Apple logo - matches LogoSocial / Apple from Figma
  static Widget appleLogo({
    Color? color,
    double? width,
    double? height,
  }) {
    return SvgPicture.asset(
      'assets/icons/apple_logo.svg',
      width: width ?? 26.4,
      height: height ?? 26.4,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
      fit: BoxFit.contain,
    );
  }

  /// Eye icon with visibility state - for password toggle
  static Widget eyeIcon({
    Color? color,
    double? width,
    double? height,
    bool isVisible = true,
  }) {
    // For now, we'll use the show icon and add a slash overlay for hidden state
    // In a real implementation, you might want separate SVG files for show/hide
    return Stack(
      alignment: Alignment.center,
      children: [
        showIcon(
          color: color,
          width: width,
          height: height,
        ),
        if (!isVisible)
          SizedBox(
            width: width ?? 24,
            height: height ?? 24,
            child: CustomPaint(
              painter: _SlashPainter(color: color ?? const Color(0xFF9CA3AF)),
            ),
          ),
      ],
    );
  }
}

/// Custom painter to draw a slash over the eye icon when hidden
class _SlashPainter extends CustomPainter {
  final Color color;

  _SlashPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Draw diagonal slash
    canvas.drawLine(
      Offset(size.width * 0.2, size.height * 0.8),
      Offset(size.width * 0.8, size.height * 0.2),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
