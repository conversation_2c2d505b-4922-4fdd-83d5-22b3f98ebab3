import 'package:flutter/material.dart';

class SocialButtonContainer extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;
  final bool isLoading;

  const SocialButtonContainer({
    required this.child,
    required this.onTap,
    this.isLoading = false,
    super.key,
  });

  @override
  State<SocialButtonContainer> createState() => _SocialButtonContainerState();
}

class _SocialButtonContainerState extends State<SocialButtonContainer> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.isLoading ? null : widget.onTap,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color:
              _isPressed
                  ? const Color(0xFFE5E7EB) // Darker gray when pressed
                  : const Color(0xFFF9FAFB), // Default light gray
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child:
              widget.isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Color(0xFFFF6982),
                      strokeWidth: 2,
                    ),
                  )
                  : widget.child,
        ),
      ),
    );
  }
}
