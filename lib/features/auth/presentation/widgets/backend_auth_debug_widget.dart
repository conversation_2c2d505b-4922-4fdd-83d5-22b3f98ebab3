import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_providers.dart';
import '../../data/models/user_model.dart';

/// Debug widget to display backend authentication status
class BackendAuthDebugWidget extends ConsumerWidget {
  const BackendAuthDebugWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);

    return authState.when(
      authenticated: (user) {
        if (user is UserModel) {
          return Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Backend Authentication Status',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    'Firebase Token',
                    user.token != null ? 'Present' : 'Missing',
                    user.token != null,
                  ),
                  _buildStatusRow(
                    'Backend Token',
                    user.backendToken != null ? 'Present' : 'Missing',
                    user.backendToken != null,
                  ),
                  _buildStatusRow(
                    'Backend Data',
                    user.backendData != null ? 'Present' : 'Missing',
                    user.backendData != null,
                  ),
                  if (user.backendData != null) ...[
                    const SizedBox(height: 8),
                    const Text(
                      'Backend Data:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        user.backendData.toString(),
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
      loading: () => const Card(
        margin: EdgeInsets.all(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('Loading authentication...'),
        ),
      ),
      unauthenticated: () => const Card(
        margin: EdgeInsets.all(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('Not authenticated'),
        ),
      ),
      error: (failure) => Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Authentication Error: ${failure.message}',
            style: const TextStyle(color: Colors.red),
          ),
        ),
      ),
      initial: () => const SizedBox.shrink(),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isSuccess) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(
              color: isSuccess ? Colors.green : Colors.orange,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
