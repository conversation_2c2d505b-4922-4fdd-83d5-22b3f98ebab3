import 'package:equatable/equatable.dart';

/// User entity representing an authenticated user
class User extends Equatable {
  /// Unique identifier for the user
  final String id;

  /// User's full name
  final String name;

  /// User's email address
  final String email;

  /// Profile photo URL
  final String? profilePhoto;

  /// User's authentication token (Firebase ID token)
  final String? token;

  /// Whether the user's email is verified
  final bool isEmailVerified;

  /// Create a new User instance
  const User({
    required this.id,
    required this.name,
    required this.email,
    this.profilePhoto,
    this.token,
    this.isEmailVerified = false,
  });

  /// Create a copy of this User with the given fields replaced
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? profilePhoto,
    String? token,
    bool? isEmailVerified,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      token: token ?? this.token,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
    );
  }

  /// List of properties used for equality checking
  @override
  List<Object?> get props => [
    id,
    name,
    email,
    profilePhoto,
    token,
    isEmailVerified,
  ];
}
