import 'package:equatable/equatable.dart';

/// Domain entity representing backend authentication result
class BackendAuthResult extends Equatable {
  final bool isSuccess;
  final String? message;
  final String? backendToken;
  final Map<String, dynamic>? userData;
  final Map<String, dynamic>? additionalData;

  const BackendAuthResult({
    required this.isSuccess,
    this.message,
    this.backendToken,
    this.userData,
    this.additionalData,
  });

  /// Factory for successful authentication
  factory BackendAuthResult.success({
    String? backendToken,
    Map<String, dynamic>? userData,
    Map<String, dynamic>? additionalData,
    String? message,
  }) {
    return BackendAuthResult(
      isSuccess: true,
      backendToken: backendToken,
      userData: userData,
      additionalData: additionalData,
      message: message,
    );
  }

  /// Factory for failed authentication
  factory BackendAuthResult.failure(String message) {
    return BackendAuthResult(
      isSuccess: false,
      message: message,
    );
  }

  @override
  List<Object?> get props => [
        isSuccess,
        message,
        backendToken,
        userData,
        additionalData,
      ];
}
