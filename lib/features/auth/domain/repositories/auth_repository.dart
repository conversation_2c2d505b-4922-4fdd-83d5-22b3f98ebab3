import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/features/auth/domain/entities/user.dart';

/// Repository interface for authentication operations
abstract class AuthRepository {
  /// Sign in with Google
  Future<Either<Failure, User>> signInWithGoogle();

  /// Sign in with Apple
  Future<Either<Failure, User>> signInWithApple();

  /// Sign out the current user
  Future<Either<Failure, void>> signOut();

  /// Get the current signed-in user
  Future<Either<Failure, User?>> getCurrentUser();

  /// Check if a user is currently signed in
  Future<bool> isSignedIn();
}
