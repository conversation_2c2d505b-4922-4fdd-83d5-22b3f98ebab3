import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/backend_auth_result.dart';

/// Repository interface for backend authentication operations
abstract class BackendAuthRepository {
  /// Authenticate with backend using Firebase ID token
  Future<Either<Failure, BackendAuthResult>> authenticateWithBackend(
    String firebaseIdToken,
  );

  /// Refresh backend authentication token
  Future<Either<Failure, BackendAuthResult>> refreshBackendToken(
    String currentBackendToken,
  );

  /// Validate backend token
  Future<Either<Failure, bool>> validateBackendToken(String backendToken);

  /// Sign out from backend
  Future<Either<Failure, void>> signOutFromBackend(String? backendToken);
}
