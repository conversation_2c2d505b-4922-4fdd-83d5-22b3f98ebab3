import 'package:equatable/equatable.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/auth_repository.dart';

/// Sign up use case with email, password, and name
class SignUpUseCase {
  final AuthRepository repository;

  SignUpUseCase(this.repository);
}

/// Parameters for the sign up use case
class SignUpParams extends Equatable {
  final String name;
  final String email;
  final String password;

  const SignUpParams({
    required this.name,
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [name, email, password];
}
