import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import '../entities/backend_auth_result.dart';
import '../repositories/backend_auth_repository.dart';

/// Use case for authenticating with backend after Firebase authentication
class AuthenticateWithBackendUseCase {
  final BackendAuthRepository repository;

  const AuthenticateWithBackendUseCase(this.repository);

  /// Execute backend authentication
  Future<Either<Failure, BackendAuthResult>> call(
    AuthenticateWithBackendParams params,
  ) async {
    return await repository.authenticateWithBackend(params.firebaseIdToken);
  }
}

/// Parameters for backend authentication use case
class AuthenticateWithBackendParams extends Equatable {
  final String firebaseIdToken;

  const AuthenticateWithBackendParams({
    required this.firebaseIdToken,
  });

  @override
  List<Object> get props => [firebaseIdToken];
}
