import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/features/auth/domain/entities/user.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/backend_auth_repository.dart';
import 'composite_google_sign_in_use_case.dart';

/// Composite use case that handles both Firebase Apple authentication and backend authentication
class CompositeAppleSignInUseCase {
  final AuthRepository authRepository;
  final BackendAuthRepository backendAuthRepository;

  const CompositeAppleSignInUseCase({
    required this.authRepository,
    required this.backendAuthRepository,
  });

  /// Execute complete Apple sign-in flow with backend authentication
  Future<Either<Failure, CompositeAuthResult>> call() async {
    try {
      debugPrint('[CompositeAppleAuth] Starting composite Apple sign-in...');

      // Step 1: Firebase Apple authentication
      final firebaseResult = await authRepository.signInWithApple();

      return firebaseResult.fold(
        (failure) {
          final failureMessage = failure.message;
          debugPrint('[CompositeAppleAuth] Firebase Apple auth failed: $failureMessage');
          return Left(failure);
        },
        (user) async {
          final typedUser = user as User?;
          final userEmail = typedUser?.email;
          if (userEmail == null) {
            return Left(AuthFailure(message: 'Apple authentication failed: No email available'));
          }
          debugPrint('[CompositeAppleAuth] Firebase Apple auth successful for: $userEmail');

          // Step 2: Backend authentication (required)
          final token = typedUser?.token;
          if (token == null) {
            debugPrint('[CompositeAppleAuth] No Firebase token available for backend auth');
            return Left(AuthFailure(message: 'Apple authentication failed: No Firebase token available'));
          }

          debugPrint('[CompositeAppleAuth] Attempting backend authentication...');

          final backendAuthResult = await backendAuthRepository.authenticateWithBackend(token);

          return backendAuthResult.fold(
            (failure) async {
              final failureMessage = failure.message;
              debugPrint('[CompositeAppleAuth] Backend auth failed: $failureMessage');
              debugPrint('[CompositeAppleAuth] Signing out Firebase user due to backend auth failure...');

              // Sign out from Firebase since backend authentication failed
              try {
                await authRepository.signOut();
                debugPrint('[CompositeAppleAuth] Firebase sign out successful after backend auth failure');
              } catch (signOutError) {
                debugPrint('[CompositeAppleAuth] Error signing out from Firebase: $signOutError');
                // Continue with the error even if sign out fails
              }

              return Left(AuthFailure(
                message: 'Backend authentication failed: $failureMessage'
              ));
            },
            (backendResult) {
              debugPrint('[CompositeAppleAuth] Backend auth successful');
              return Right(CompositeAuthResult(
                user: typedUser!,
                backendAuthResult: backendResult,
              ));
            },
          );
        },
      );
    } catch (e) {
      debugPrint('[CompositeAppleAuth] Unexpected error: $e');
      return Left(AuthFailure(message: 'Apple authentication failed: $e'));
    }
  }
}
