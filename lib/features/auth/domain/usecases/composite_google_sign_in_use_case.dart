import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../entities/backend_auth_result.dart';
import '../repositories/auth_repository.dart';
import '../repositories/backend_auth_repository.dart';

/// Composite use case that handles both Firebase and backend authentication
class CompositeGoogleSignInUseCase {
  final AuthRepository authRepository;
  final BackendAuthRepository backendAuthRepository;

  const CompositeGoogleSignInUseCase({
    required this.authRepository,
    required this.backendAuthRepository,
  });

  /// Execute complete Google sign-in flow with backend authentication
  Future<Either<Failure, CompositeAuthResult>> call() async {
    try {
      debugPrint('[CompositeAuth] Starting composite Google sign-in...');

      // Step 1: Firebase authentication
      final firebaseResult = await authRepository.signInWithGoogle();

      return firebaseResult.fold(
        (failure) {
          final failureMessage = failure.message;
          debugPrint('[CompositeAuth] Firebase auth failed: $failureMessage');
          return Left(failure);
        },
        (user) async {
          final typedUser = user as User?;
          final userEmail = typedUser?.email;
          if (userEmail == null) {
            return Left(AuthFailure(message: 'Authentication failed: No email available'));
          }
          debugPrint('[CompositeAuth] Firebase auth successful for: $userEmail');

          // Step 2: Backend authentication (required)
          final token = typedUser?.token;
          if (token == null) {
            debugPrint('[CompositeAuth] No Firebase token available for backend auth');
            return Left(AuthFailure(message: 'Authentication failed: No Firebase token available'));
          }

          debugPrint('[CompositeAuth] Attempting backend authentication...');

          final backendAuthResult = await backendAuthRepository.authenticateWithBackend(token);

          return backendAuthResult.fold(
            (failure) async {
              final failureMessage = (failure as Failure?)?.message ?? 'Unknown error';
              debugPrint('[CompositeAuth] Backend auth failed: $failureMessage');
              debugPrint('[CompositeAuth] Signing out Firebase user due to backend auth failure...');

              // Sign out from Firebase since backend authentication failed
              try {
                await authRepository.signOut();
                debugPrint('[CompositeAuth] Firebase sign out successful after backend auth failure');
              } catch (signOutError) {
                debugPrint('[CompositeAuth] Error signing out from Firebase: $signOutError');
                // Continue with the error even if sign out fails
              }

              return Left(AuthFailure(
                message: 'Backend authentication failed: $failureMessage'
              ));
            },
            (backendResult) {
              debugPrint('[CompositeAuth] Backend auth successful');
              return Right(CompositeAuthResult(
                user: typedUser!,
                backendAuthResult: backendResult,
              ));
            },
          );
        },
      );
    } catch (e) {
      debugPrint('[CompositeAuth] Unexpected error: $e');
      return Left(AuthFailure(message: 'Authentication failed: $e'));
    }
  }
}

/// Result of composite authentication containing both Firebase and backend data
class CompositeAuthResult {
  final User user;
  final BackendAuthResult backendAuthResult;

  const CompositeAuthResult({
    required this.user,
    required this.backendAuthResult,
  });

  /// Check if backend authentication was successful
  bool get hasBackendAuth => backendAuthResult.isSuccess;

  /// Get backend token
  String get backendToken => backendAuthResult.backendToken ?? '';

  /// Get backend user data
  Map<String, dynamic> get backendUserData => backendAuthResult.userData ?? {};

  /// Get all backend data
  Map<String, dynamic> get backendData => backendAuthResult.additionalData ?? {};
}
