import 'package:dartz/dartz.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';
import 'package:flutter_travelgator/features/auth/domain/entities/user.dart';
import 'package:flutter_travelgator/features/auth/domain/repositories/auth_repository.dart';

/// Use case for sign in with Apple
class AppleSignInUseCase {
  final AuthRepository repository;

  AppleSignInUseCase(this.repository);

  /// Execute Apple sign-in
  Future<Either<Failure, User>> call() async {
    return await repository.signInWithApple();
  }
}
