import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../core/theme/app_theme.dart';

part 'theme_providers.g.dart';

// Theme mode notifier using Riverpod code generation
@riverpod
class ThemeModeNotifier extends _$ThemeModeNotifier {
  @override
  ThemeMode build() {
    return ThemeMode.system;
  }

  void setThemeMode(ThemeMode mode) {
    state = mode;
  }
}

// Theme data providers using Riverpod code generation
@riverpod
ThemeData lightTheme(Ref ref) {
  return AppTheme.light;
}

@riverpod
ThemeData darkTheme(Ref ref) {
  return AppTheme.dark;
}

// Current theme provider based on theme mode
@riverpod
ThemeData currentTheme(Ref ref) {
  final themeMode = ref.watch(themeModeNotifierProvider);
  final brightness =
      WidgetsBinding.instance.platformDispatcher.platformBrightness;

  switch (themeMode) {
    case ThemeMode.light:
      return ref.watch(lightThemeProvider);
    case ThemeMode.dark:
      return ref.watch(darkThemeProvider);
    case ThemeMode.system:
      return brightness == Brightness.dark
          ? ref.watch(darkThemeProvider)
          : ref.watch(lightThemeProvider);
  }
}
