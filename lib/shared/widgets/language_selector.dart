import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/providers/locale_provider.dart';

class LanguageSelector extends ConsumerWidget {
  const LanguageSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(localeProvider);

    return PopupMenuButton<Locale>(
      icon: const Icon(Icons.language),
      onSelected: (Locale locale) {
        ref.read(localeProvider.notifier).state = locale;
      },
      itemBuilder:
          (BuildContext context) => [
            PopupMenuItem<Locale>(
              value: const Locale('en'),
              child: Row(
                children: [
                  const Text('🇺🇸'),
                  const SizedBox(width: 8),
                  const Text('English'),
                  if (currentLocale.languageCode == 'en')
                    const Icon(Icons.check, size: 16),
                ],
              ),
            ),
            PopupMenuItem<Locale>(
              value: const Locale('vi'),
              child: Row(
                children: [
                  const Text('🇻🇳'),
                  const SizedBox(width: 8),
                  const Text('Tiếng Việt'),
                  if (currentLocale.languageCode == 'vi')
                    const Icon(Icons.check, size: 16),
                ],
              ),
            ),
          ],
    );
  }
}
