import 'package:flutter/material.dart';
import 'card_item.dart';

class CardExamplePage extends StatelessWidget {
  const CardExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Card Examples')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CardItem(
              imageUrl: 'assets/images/card_image.png',
              title: 'Beautiful Destination',
              subtitle: 'Explore this amazing location',
              onTap: () {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Card tapped!')));
              },
            ),
            const SizedBox(height: 16),
            CardItem(
              title: 'Card without image',
              subtitle: 'This card uses the default image',
            ),
            const SizedBox(height: 16),
            const CardItem(subtitle: 'Card with only subtitle'),
          ],
        ),
      ),
    );
  }
}
