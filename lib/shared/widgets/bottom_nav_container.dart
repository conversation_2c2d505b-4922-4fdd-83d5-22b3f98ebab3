import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers/tab_state_provider.dart';
import '../../features/home/<USER>/widgets/home_bottom_nav_bar.dart';

/// A reusable container widget that adds the bottom navigation bar to child screens
/// Can be used in any feature that needs to maintain the bottom navigation
class BottomNavContainer extends ConsumerStatefulWidget {
  final Widget child;
  final int tabIndex;

  const BottomNavContainer({
    super.key,
    required this.tabIndex,
    required this.child,
  });

  @override
  ConsumerState<BottomNavContainer> createState() => _BottomNavContainerState();
}

class _BottomNavContainerState extends ConsumerState<BottomNavContainer> {
  @override
  void initState() {
    super.initState();
    // Schedule tab update for the next frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && ref.read(tabStateProvider) != widget.tabIndex) {
        ref.read(tabStateProvider.notifier).setTab(widget.tabIndex);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: const HomeBottomNavBar(),
    );
  }
}
