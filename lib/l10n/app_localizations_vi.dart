// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get appTitle => 'TravelGator';

  @override
  String get welcomeMessage => 'Chào mừng đến với TravelGator';

  @override
  String get signIn => 'Đăng Nhập';

  @override
  String get signUp => 'Đăng Ký';

  @override
  String get email => 'Email';

  @override
  String get password => 'Mật Khẩu';

  @override
  String get name => 'Tên';

  @override
  String get forgotPassword => 'Quên Mật Khẩu?';

  @override
  String get orContinueWith => 'hoặc tiếp tục với';

  @override
  String get dontHaveAccount => 'Bạn chưa có tài khoản?';

  @override
  String get alreadyHaveAccount => 'Bạn đã có tài khoản?';

  @override
  String get createAccount => 'Tạo Tài Khoản';

  @override
  String get getStarted => 'Bắt Đầu';
}
