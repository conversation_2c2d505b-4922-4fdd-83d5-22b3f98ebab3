# Clean Architecture Overview (Riverpod 2.0)

## Layers

```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│  Presentation Layer                                     │
│  ┌─────────────────────────────────────────────────┐   │
│  │                                                 │   │
│  │  UI Components (Pages, Widgets)                 │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
│  ┌───────────────────▼─────────────────────────────┐   │
│  │                                                 │   │
│  │  State Management (Riverpod Controllers)        │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
└──────────────────────┼──────────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────────┐
│                                                         │
│  Domain Layer                                           │
│  ┌─────────────────────────────────────────────────┐   │
│  │                                                 │   │
│  │  Use Cases                                      │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
│  ┌───────────────────▼─────────────────────────────┐   │
│  │                                                 │   │
│  │  Entities / Business Models                     │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
│  ┌───────────────────▼─────────────────────────────┐   │
│  │                                                 │   │
│  │  Repository Interfaces                          │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
└──────────────────────┼──────────────────────────────────┘
                       │
┌──────────────────────▼──────────────────────────────────┐
│                                                         │
│  Data Layer                                             │
│  ┌─────────────────────────────────────────────────┐   │
│  │                                                 │   │
│  │  Repository Implementations                     │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
│  ┌───────────────────▼─────────────────────────────┐   │
│  │                                                 │   │
│  │  Data Models                                    │   │
│  │                                                 │   │
│  └───────────────────┬─────────────────────────────┘   │
│                      │                                  │
│  ┌───────────────────▼─────────────────────────────┐   │
│  │                                                 │   │
│  │  Data Sources (Remote, Local)                   │   │
│  │                                                 │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## Dependencies Between Layers

- **Presentation Layer** depends on **Domain Layer**
- **Domain Layer** is independent
- **Data Layer** depends on **Domain Layer**

All providers are globally defined using Provider, StateNotifierProvider, or FutureProvider as needed.

## Data Flow

1. <PERSON><PERSON> interacts with ref.watch(...) or ref.read(...) to call controllers.
2. Controllers (StateNotifiers) invoke Use Cases from the Domain Layer.
3. Use Cases use Repository Interfaces to request data.
4. Repository Implementations in the Data Layer handle logic.
5. Data Sources fetch/store external data (API, DB).
6. Results propagate back through the use case → controller → UI.
7. State changes update UI reactively via ref.watch(...).

## Core Principles

1. **Inversion of Control**: Riverpod providers inject dependencies declaratively.
2. **Separation of Concerns**: UI, logic, and data handling are cleanly separated.
3. **Scalability**: Each feature/module scales independently.
4. **Testability**: Riverpod allows easy provider overrides for unit testing.
5. **Framework Independence**: Domain Layer is Dart-only and testable without Flutter.

## Key Components

| Layer        | Component                  | Riverpod Role                                         |
| ------------ | -------------------------- | ----------------------------------------------------- |
| Presentation | UI Widgets                 | ConsumerWidget, ref.watch()                           |
|              | State Controllers          | StateNotifier, StateNotifierProvider                  |
| Domain       | Entities                   | Pure Dart (no Riverpod)                               |
|              | Use Cases                  | Pure Dart (called by controllers)                     |
|              | Repository Interfaces      | Abstract contracts                                    |
| Data         | Repository Implementations | Provided via Provider()                               |
|              | Data Models                | Internal to Data layer                                |
|              | Data Sources               | Injected with Provider, e.g., dio, shared_preferences |
