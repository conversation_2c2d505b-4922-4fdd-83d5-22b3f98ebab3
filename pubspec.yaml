name: flutter_travelgator
description: 'Travelgator development application'
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.1.0
  font_awesome_flutter: ^10.1.0
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  equatable: ^2.0.5
  dartz: ^0.10.1
  get_it: ^8.0.3
  flutter_bloc: ^9.1.1
  shared_preferences: ^2.5.3
  http: ^1.1.0
  intl: ^0.19.0
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  dio: ^5.3.2
  freezed: ^3.0.6
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  go_router: ^15.1.2
  graphql_flutter: ^5.2.0
  shimmer: ^3.0.0
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.3
  google_sign_in: ^6.2.1
  google_sign_in_ios: ^5.9.0
  sign_in_with_apple: ^6.1.3
  flutter_native_splash: ^2.4.6
  local_auth: ^2.1.6
  auto_size_text: ^3.0.0
  webview_flutter: ^4.4.2
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.8
  riverpod_generator: ^2.3.9
  json_serializable: ^6.7.1
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/icons/cart_header/
    - assets/images/travelgator_logo.svg
    - assets/images/flags/
  generate: true
