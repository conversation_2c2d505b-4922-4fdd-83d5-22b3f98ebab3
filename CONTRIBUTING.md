# Contributing to Flutter Clean Architecture App

Thank you for considering contributing to this project! Here are some guidelines to help you get started.

## Project Structure

This project follows Clean Architecture principles. Please make sure to respect the existing architecture when adding new features:

- `lib/core`: Core functionality, utilities, configs
- `lib/domain`: Business logic (entities, repositories interfaces, use cases)
- `lib/data`: Data layer (models, repository implementations, data sources)
- `lib/presentation`: UI layer (pages, widgets, blocs)
- `lib/di`: Dependency injection

## Coding Standards

1. **File Naming**: Use snake_case for file names
2. **Class Naming**: Use PascalCase for class names
3. **Variable/Method Naming**: Use camelCase for variables and methods
4. **Follow Flutter's style guide**: [Effective Dart](https://dart.dev/guides/language/effective-dart/style)

## Architecture Guidelines

1. **Domain Layer**:

   - Must be independent of any external framework
   - Contains business entities, repository interfaces, and use cases
   - No dependencies on other layers

2. **Data Layer**:

   - Implements repository interfaces from the domain layer
   - Handles data sources (API, database, etc.)
   - Maps data models to domain entities

3. **Presentation Layer**:
   - Uses BLoC pattern for state management
   - UI should only depend on the domain layer, never on the data layer
   - Each feature should have its own BLoC

## Testing

- Write unit tests for each use case
- Write unit tests for repository implementations
- Write unit tests for BLoCs
- Use integration tests for important user flows

## Git Workflow

1. Create a new branch for each feature or bug fix
2. Use descriptive branch names (e.g., `feature/user-authentication`, `fix/login-validation`)
3. Make small, focused commits with clear messages
4. Open a pull request for review
5. Update based on review comments

## Adding New Features

1. Define entities in the domain layer
2. Create repository interfaces in the domain layer
3. Implement use cases in the domain layer
4. Create data models in the data layer
5. Implement repositories in the data layer
6. Create BLoCs in the presentation layer
7. Build UI components in the presentation layer
8. Register dependencies in the DI container

## Submitting Changes

1. Ensure your code passes all tests
2. Update documentation if needed
3. Create a pull request with a clear description of the changes

Thank you for contributing!
