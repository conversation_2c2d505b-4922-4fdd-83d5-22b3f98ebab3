Welcome to firepit v1.1.0!
Doing JSO<PERSON> parses for version checks at /snapshot/firepit/vendor/node_modules/firebase-tools/package.json
color-support,is-ci,jsdoc,mime,rc,rimraf,abbrev,abort-controller,accepts,acorn,acorn-jsx,agent-base,agentkeepalive,aggregate-error,ajv,ajv-formats,ansi-align,ansi-escapes,ansi-regex,ansi-styles,ansicolors,anymatch,aproba,archiver,archiver-utils,are-we-there-yet,argparse,array-flatten,arrify,as-array,asn1,assert-plus,ast-types,async,async-lock,asynckit,aws-sign2,aws4,balanced-match,base64-js,basic-auth,basic-auth-connect,basic-ftp,bcrypt-pbkdf,bignumber.js,binary-extensions,bl,bluebird,body-parser,boxen,brace-expansion,braces,buffer,buffer-crc32,buffer-equal-constant-time,bytes,cacache,call-bind,call-me-maybe,camelcase,cardinal,caseless,catharsis,chalk,chardet,chokidar,chownr,ci-info,cjson,clean-stack,cli-boxes,cli-cursor,cli-spinners,cli-table,cli-table3,cli-width,cliui,clone,color,color-convert,color-name,color-string,colorette,colors,colorspace,combined-stream,commander,compress-commons,compressible,compression,concat-map,config-chain,configstore,connect,console-control-strings,content-disposition,content-type,cookie,cookie-signature,core-util-is,cors,crc-32,crc32-stream,cross-env,cross-spawn,crypto-random-string,csv-parse,dashdash,data-uri-to-buffer,debug,deep-extend,deep-freeze,deep-is,defaults,degenerator,delayed-stream,delegates,depd,destroy,dot-prop,duplexify,eastasianwidth,ecc-jsbn,ecdsa-sig-formatter,ee-first,emoji-regex,enabled,encodeurl,encoding,end-of-stream,entities,env-paths,err-code,escalade,escape-goat,escape-html,escape-string-regexp,escodegen,eslint-visitor-keys,espree,esprima,estraverse,esutils,etag,event-target-shim,events-listener,exegesis,exegesis-express,exponential-backoff,express,extend,external-editor,extsprintf,fast-deep-equal,fast-json-stable-stringify,fast-levenshtein,fast-text-encoding,fast-url-parser,fecha,figures,filesize,fill-range,finalhandler,firebase-tools,fn.name,foreground-child,forever-agent,form-data,forwarded,fresh,fs-constants,fs-extra,fs-minipass,fs.realpath,function-bind,gauge,gaxios,gcp-metadata,get-caller-file,get-intrinsic,get-uri,getpass,glob,glob-parent,glob-slash,glob-slasher,global-dirs,google-auth-library,google-gax,google-p12-pem,graceful-fs,gtoken,har-schema,har-validator,has,has-flag,has-proto,has-symbols,has-unicode,has-yarn,heap-js,http-cache-semantics,http-errors,http-proxy-agent,http-signature,https-proxy-agent,humanize-ms,iconv-lite,ieee754,import-lazy,imurmurhash,indent-string,inflight,inherits,ini,inquirer,install-artifact-from-github,ip,ip-regex,ipaddr.js,is-arrayish,is-binary-path,is-extglob,is-fullwidth-code-point,is-glob,is-installed-globally,is-interactive,is-lambda,is-npm,is-number,is-obj,is-path-inside,is-stream,is-stream-ended,is-typedarray,is-unicode-supported,is-url,is-wsl,is-yarn-global,is2,isarray,isexe,isomorphic-fetch,isstream,jackspeak,jju,join-path,js-yaml,js2xmlparser,jsbn,json-bigint,json-parse-helpfulerror,json-ptr,json-schema,json-schema-traverse,json-stringify-safe,jsonfile,jsonwebtoken,jsprim,jwa,jws,klaw,kuler,lazystream,leven,levn,libsodium,libsodium-wrappers,linkify-it,lodash,lodash._objecttypes,lodash.camelcase,lodash.defaults,lodash.difference,lodash.flatten,lodash.isobject,lodash.isplainobject,lodash.snakecase,lodash.union,log-symbols,logform,long,lru-cache,make-dir,make-fetch-happen,markdown-it,markdown-it-anchor,marked,marked-terminal,mdurl,media-typer,merge-descriptors,methods,mime-db,mime-types,mimic-fn,minimatch,minimist,minipass,minipass-collect,minipass-fetch,minipass-flush,minipass-pipeline,minipass-sized,minizlib,mkdirp,morgan,ms,mute-stream,nan,negotiator,netmask,nice-try,node-emoji,node-fetch,node-forge,node-gyp,nopt,normalize-path,npmlog,oauth-sign,object-assign,object-hash,object-inspect,on-finished,on-headers,once,one-time,onetime,open,openapi3-ts,optionator,ora,os-tmpdir,p-defer,p-limit,p-map,pac-proxy-agent,pac-resolver,parseurl,path-is-absolute,path-key,path-scurry,path-to-regexp,performance-now,picomatch,portfinder,prelude-ls,process-nextick-args,progress,promise-breaker,promise-retry,proto-list,proto3-json-serializer,protobufjs,protobufjs-cli,proxy-addr,proxy-agent,proxy-from-env,psl,pump,punycode,pupa,qs,range-parser,raw-body,re2,readable-stream,readdir-glob,readdirp,redeyed,registry-auth-token,registry-url,request,require-directory,require-from-string,requizzle,restore-cursor,retry,retry-request,router,run-async,rxjs,safe-buffer,safe-stable-stringify,safer-buffer,semver,semver-diff,send,serve-static,set-blocking,setprototypeof,shebang-command,shebang-regex,side-channel,signal-exit,simple-swizzle,smart-buffer,socks,socks-proxy-agent,source-map,sprintf-js,sshpk,ssri,stack-trace,statuses,stream-chain,stream-json,stream-shift,string-width,string-width-cjs,string_decoder,strip-ansi,strip-ansi-cjs,strip-json-comments,superstatic,supports-color,supports-hyperlinks,tar,tar-stream,tcp-port-used,text-hex,through,tmp,to-regex-range,toidentifier,tough-cookie,toxic,tr46,triple-beam,tslib,tunnel-agent,tweetnacl,type-check,type-fest,type-is,typedarray-to-buffer,uc.micro,uglify-js,underscore,unique-filename,unique-slug,unique-string,universal-analytics,universalify,unpipe,update-notifier-cjs,uri-js,url-join,util-deprecate,utils-merge,uuid,valid-url,vary,verror,wcwidth,webidl-conversions,whatwg-fetch,whatwg-url,which,wide-align,widest-line,winston,winston-transport,word-wrap,wrap-ansi,wrap-ansi-cjs,wrappy,write-file-atomic,ws,xdg-basedir,xmlcreate,y18n,yallist,yaml,yargs,yargs-parser,yocto-queue,zip-stream,@apidevtools,@babel,@colors,@dabh,@google-cloud,@grpc,@isaacs,@jsdevtools,@jsdoc,@npmcli,@opentelemetry,@pkgjs,@pnpm,@protobufjs,@tootallnate,@types
Installed ft@12.4.7 and packaged ft@12.4.7
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
ShellJSInternalError: ENOENT: no such file or directory, unlink '/Users/<USER>/.cache/firebase/runtime/node'
ShellJSInternalError: ENOENT: no such file or directory, unlink '/Users/<USER>/.cache/firebase/runtime/npm'
ShellJSInternalError: ENOENT: no such file or directory, unlink '/Users/<USER>/.cache/firebase/runtime/npm.bat'
Runtime binaries created.
/usr/local/bin/firebase
/usr/local/bin/firebase,/snapshot/firepit/firepit.js,--version
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
ShellJSInternalError: ENOENT: no such file or directory, chmod '/Users/<USER>/.cache/firebase/runtime/node.js'