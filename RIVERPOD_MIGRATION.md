# Riverpod 2.0 Migration Guide

This project has been updated to use Riverpod 2.0 with code generation. This guide explains the changes and how to leverage the new features.

## What's New

1. **Code Generation**: Uses `@riverpod` annotations to generate provider code automatically.
2. **Type Safety**: Improved type safety with automatically generated provider references.
3. **AsyncNotifier**: Better handling of asynchronous state.
4. **Simplified Provider Creation**: Less boilerplate, more maintainable code.

## Migration Strategy

The project follows a gradual migration approach:

1. Old providers are marked with `@Deprecated` annotations but still work.
2. New providers using code generation are available in parallel.
3. You can migrate code gradually from old to new providers.

## How to Use the New Providers

### StateProvider → Notifier Class

Before:

```dart
final counterProvider = StateProvider<int>((ref) => 0);

// Usage
ref.read(counterProvider.notifier).state++;
```

After:

```dart
@riverpod
class Counter extends _$Counter {
  @override
  int build() => 0;

  void increment() => state++;
}

// Usage
ref.read(counterProvider.notifier).increment();
```

### Provider → Function

Before:

```dart
final userRepositoryProvider = Provider<UserRepository>((ref) {
  return UserRepositoryImpl();
});
```

After:

```dart
@riverpod
UserRepository userRepository(UserRepositoryRef ref) {
  return UserRepositoryImpl();
}
```

### AsyncValue Support

```dart
@riverpod
Future<User> fetchUser(FetchUserRef ref, String userId) async {
  // Fetch user data
  return user;
}

// Usage (with AsyncValue in the UI)
Consumer(
  builder: (context, ref, child) {
    final userAsync = ref.watch(fetchUserProvider(userId));

    return userAsync.when(
      data: (user) => Text(user.name),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  },
)
```

## How to Generate Code

After making changes to any file with `@riverpod` annotations, run:

```bash
flutter pub run build_runner build
```

Or for continuous watching:

```bash
flutter pub run build_runner watch
```

## Best Practices

1. Use `ref.watch()` in build methods to react to changes.
2. Use `ref.read()` in event handlers to avoid unnecessary rebuilds.
3. Use AsyncValue's `.when()` method to handle loading/error states.
4. Keep provider files clean and focused on a single responsibility.

## Further Reading

- [Riverpod Documentation](https://riverpod.dev/)
- [Code Generation Guide](https://riverpod.dev/docs/concepts/about_code_generation)
- [AsyncValue Documentation](https://riverpod.dev/docs/concepts/providers#asyncvalue)
