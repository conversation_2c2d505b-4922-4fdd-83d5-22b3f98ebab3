# Authentication Flow - Firebase + Backend

## Tổng quan

Dự án sử dụng composite authentication flow kết hợp Firebase Authentication và Backend Authentication để đảm bảo tính bảo mật cao và quản lý user data hiệu quả.

## Flow Authentication

### 1. Composite Authentication (Khuyến nghị)

#### Google Sign-In Flow:
```
1. User nhấn "Sign in with Google"
2. Firebase Google Authentication
   ├─ Thành công → Tiếp tục bước 3
   └─ Thất bại → Hiển thị lỗi, dừng flow
3. Backend Authentication với Firebase ID Token
   ├─ Thành công → Lưu enhanced user data, cache, set authenticated
   └─ Thất bại → Sign out Firebase, clear cache, hiển thị lỗi
```

#### Apple Sign-In Flow:
```
1. User nhấn "Sign in with Apple"
2. Firebase Apple Authentication
   ├─ Thành công → Tiế<PERSON> tục bước 3
   └─ Thất bại → Hiển thị lỗi, dừng flow
3. Backend Authentication với Firebase ID Token
   ├─ Thành công → Lưu enhanced user data, cache, set authenticated
   └─ Thất bại → Sign out Firebase, clear cache, hiển thị lỗi
```

### 2. App Startup Authentication Check

```
1. App khởi động
2. Kiểm tra cached enhanced user
   ├─ Có cache và có backend auth → Kiểm tra Firebase user
   │   ├─ Firebase user còn valid → Sử dụng cached data
   │   └─ Firebase user không valid → Clear cache, tiếp tục bước 3
   └─ Không có cache → Tiếp tục bước 3
3. Kiểm tra Firebase user
   ├─ Không có Firebase user → Set unauthenticated
   └─ Có Firebase user → Tiếp tục bước 4
4. Backend Authentication với Firebase token
   ├─ Thành công → Cache enhanced user, set authenticated
   └─ Thất bại → Sign out Firebase, set unauthenticated
```

## Các thành phần chính

### 1. CompositeGoogleSignInUseCase
- Xử lý flow Google Sign-In với Firebase + Backend
- Tự động sign out Firebase khi backend auth thất bại
- Trả về `CompositeAuthResult` chứa cả Firebase và Backend data

### 2. CompositeAppleSignInUseCase
- Xử lý flow Apple Sign-In với Firebase + Backend
- Tự động sign out Firebase khi backend auth thất bại
- Trả về `CompositeAuthResult` chứa cả Firebase và Backend data

### 3. AuthNotifier
- Quản lý authentication state
- Xử lý caching và error handling
- Đảm bảo consistency giữa Firebase và Backend auth

### 4. EnhancedUserModel
- Kết hợp Firebase user data và Backend user data
- Hỗ trợ caching để tăng performance
- Chứa thông tin backend token và user data

## Tính năng bảo mật

### 1. Automatic Cleanup
- Khi backend authentication thất bại, Firebase authentication sẽ được xóa hoàn toàn
- Cache sẽ được clear để tránh inconsistent state
- User sẽ được redirect về unauthenticated state

### 2. Token Validation
- Firebase ID token được validate với backend
- Backend token được cache để sử dụng cho các API calls
- Automatic refresh token handling

### 3. Startup Validation
- Kiểm tra authentication state khi app khởi động
- Validate cả Firebase và Backend authentication
- Clear invalid cache data

## Cách sử dụng

### 1. Sign In
```dart
// Composite authentication (khuyến nghị)
await ref.read(authNotifierProvider.notifier).signInWithGoogle();
await ref.read(authNotifierProvider.notifier).signInWithApple();

// Firebase-only authentication (cho modal)
await ref.read(authNotifierProvider.notifier).signInWithGoogleFirebaseOnly();
await ref.read(authNotifierProvider.notifier).signInWithAppleFirebaseOnly();
```

### 2. Backend Authentication
```dart
// Authenticate với backend sử dụng Firebase token hiện tại
final success = await ref.read(authNotifierProvider.notifier).authenticateWithBackend();
```

### 3. Sign Out
```dart
// Sign out hoàn toàn (Firebase + clear cache)
await ref.read(authNotifierProvider.notifier).signOut();
```

### 4. Kiểm tra Authentication State
```dart
final authState = ref.watch(authNotifierProvider);
authState.when(
  initial: () => CircularProgressIndicator(),
  loading: () => CircularProgressIndicator(),
  authenticated: (user) => HomeScreen(user: user),
  unauthenticated: () => LoginScreen(),
  error: (failure) => ErrorWidget(failure.message),
);
```

## Error Handling

### 1. Firebase Authentication Errors
- Network errors
- User cancelled
- Invalid credentials
- Account disabled

### 2. Backend Authentication Errors
- Invalid Firebase token
- Backend server errors
- User not found in backend
- Permission denied

### 3. Cache Errors
- Storage errors
- Data corruption
- Version mismatch

## Testing

Xem file `test/features/auth/presentation/providers/auth_providers_test.dart` để biết cách test các scenarios:
- Composite authentication success
- Backend authentication failure
- Firebase authentication failure
- Startup authentication validation
