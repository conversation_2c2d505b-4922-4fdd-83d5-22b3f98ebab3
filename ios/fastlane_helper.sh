#!/bin/bash

# TravelGator iOS Fastlane Helper Script
# This script provides easy access to common Fastlane operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the ios directory
if [ ! -f "Gemfile" ] || [ ! -d "fastlane" ]; then
    print_error "Please run this script from the ios directory"
    exit 1
fi

# Function to show usage
show_usage() {
    echo "TravelGator iOS Fastlane Helper"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  setup       - Initial setup (install dependencies)"
    echo "  build       - Build the iOS app"
    echo "  test        - Run tests"
    echo "  beta        - Deploy to TestFlight"
    echo "  release     - Deploy to App Store"
    echo "  certs       - Manage certificates"
    echo "  sync        - Sync certificates and provisioning profiles"
    echo "  screenshots - Generate App Store screenshots"
    echo "  lanes       - Show available lanes"
    echo "  help        - Show this help message"
    echo ""
}

# Main script logic
case "$1" in
    "setup")
        print_status "Setting up Fastlane dependencies..."
        bundle install --path vendor/bundle
        print_success "Setup complete!"
        print_warning "Don't forget to configure your .env file!"
        ;;
    "build")
        print_status "Building iOS app..."
        bundle exec fastlane build
        ;;
    "test")
        print_status "Running tests..."
        bundle exec fastlane test
        ;;
    "beta")
        print_status "Deploying to TestFlight..."
        bundle exec fastlane beta
        ;;
    "release")
        print_status "Deploying to App Store..."
        bundle exec fastlane release
        ;;
    "certs")
        print_status "Managing certificates..."
        bundle exec fastlane certificates
        ;;
    "sync")
        print_status "Syncing certificates..."
        bundle exec fastlane sync_certificates
        ;;
    "screenshots")
        print_status "Generating screenshots..."
        bundle exec fastlane screenshots
        ;;
    "lanes")
        bundle exec fastlane lanes
        ;;
    "help"|"")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
