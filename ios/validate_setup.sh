#!/bin/bash

# TravelGator iOS Fastlane Setup Validation Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

echo "🔍 TravelGator iOS Fastlane Setup Validation"
echo "=============================================="
echo ""

# Check if we're in the ios directory
if [ ! -f "Gemfile" ] || [ ! -d "fastlane" ]; then
    print_error "Please run this script from the ios directory"
    exit 1
fi

# Check Ruby
print_status "Checking Ruby installation..."
if command -v ruby &> /dev/null; then
    RUBY_VERSION=$(ruby --version)
    print_success "Ruby found: $RUBY_VERSION"
else
    print_error "Ruby not found"
    exit 1
fi

# Check Bundler
print_status "Checking Bundler..."
if command -v bundle &> /dev/null; then
    print_success "Bundler found"
else
    print_error "Bundler not found. Install with: gem install bundler"
    exit 1
fi

# Check if gems are installed
print_status "Checking Fastlane installation..."
if [ -d "vendor/bundle" ]; then
    print_success "Gems installed in vendor/bundle"
    
    # Test Fastlane
    if bundle exec fastlane --version &> /dev/null; then
        FASTLANE_VERSION=$(bundle exec fastlane --version | tail -n 1)
        print_success "Fastlane working: $FASTLANE_VERSION"
    else
        print_error "Fastlane not working properly"
        exit 1
    fi
else
    print_warning "Gems not installed. Run: ./fastlane_helper.sh setup"
fi

# Check Fastlane configuration files
print_status "Checking Fastlane configuration..."

if [ -f "fastlane/Appfile" ]; then
    print_success "Appfile found"
else
    print_error "Appfile missing"
fi

if [ -f "fastlane/Fastfile" ]; then
    print_success "Fastfile found"
else
    print_error "Fastfile missing"
fi

if [ -f "fastlane/Matchfile" ]; then
    print_success "Matchfile found"
else
    print_error "Matchfile missing"
fi

# Check environment file
print_status "Checking environment configuration..."
if [ -f "fastlane/.env" ]; then
    print_success ".env file found"
    
    # Check for required variables
    if grep -q "APPLE_ID=" fastlane/.env; then
        print_success "APPLE_ID configured"
    else
        print_warning "APPLE_ID not configured in .env"
    fi
    
    if grep -q "MATCH_GIT_URL=" fastlane/.env; then
        print_success "MATCH_GIT_URL configured"
    else
        print_warning "MATCH_GIT_URL not configured in .env"
    fi
else
    print_warning ".env file not found. Copy from .env.default and configure"
fi

# Check Xcode project
print_status "Checking Xcode project..."
if [ -f "Runner.xcodeproj/project.pbxproj" ]; then
    print_success "Xcode project found"
else
    print_error "Xcode project not found"
fi

if [ -f "Runner.xcworkspace/contents.xcworkspacedata" ]; then
    print_success "Xcode workspace found"
else
    print_error "Xcode workspace not found"
fi

# Check Flutter
print_status "Checking Flutter..."
if command -v flutter &> /dev/null; then
    FLUTTER_VERSION=$(flutter --version | head -n 1)
    print_success "Flutter found: $FLUTTER_VERSION"
else
    print_warning "Flutter not found in PATH"
fi

# Check CocoaPods
print_status "Checking CocoaPods..."
if [ -f "Podfile.lock" ]; then
    print_success "CocoaPods dependencies installed"
else
    print_warning "CocoaPods dependencies not installed. Run: pod install"
fi

echo ""
echo "🎉 Validation complete!"
echo ""
echo "Next steps:"
echo "1. Configure your .env file if not done already"
echo "2. Set up certificates: ./fastlane_helper.sh sync"
echo "3. Test build: ./fastlane_helper.sh build"
echo ""
