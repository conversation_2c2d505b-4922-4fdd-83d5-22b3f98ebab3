# Fastlane for TravelGator iOS

<div align="center">
  <img src="https://docs.fastlane.tools/img/fastlane_text.png" width="400">

  **Automated iOS Build & Deployment for TravelGator**

  [![Fastlane](https://img.shields.io/badge/fastlane-2.227.2-blue.svg)](https://fastlane.tools)
  [![Platform](https://img.shields.io/badge/platform-iOS-lightgrey.svg)](https://developer.apple.com/ios/)
  [![Flutter](https://img.shields.io/badge/flutter-3.29.2-blue.svg)](https://flutter.dev)
</div>

## 🚀 Overview

This directory contains the complete Fastlane configuration for automating iOS builds, testing, and deployments for the TravelGator Flutter application. Fastlane eliminates manual work and reduces human error in the iOS release process.

## 📋 Table of Contents

- [Quick Start](#-quick-start)
- [Available Commands](#-available-commands)
- [Configuration](#-configuration)
- [Lanes Documentation](#-lanes-documentation)
- [Certificate Management](#-certificate-management)
- [CI/CD Integration](#-cicd-integration)
- [Troubleshooting](#-troubleshooting)
- [Contributing](#-contributing)

## ⚡ Quick Start

### Prerequisites

- macOS with Xcode installed
- Apple Developer account
- Flutter SDK
- Ruby (pre-installed on macOS)

### Installation

```bash
# Navigate to iOS directory
cd ios

# Install dependencies
./fastlane_helper.sh setup

# Validate setup
./validate_setup.sh

# Configure environment
cp fastlane/.env.default fastlane/.env
# Edit .env with your credentials
```

### First Run

```bash
# Set up certificates (first time only)
./fastlane_helper.sh sync

# Build the app
./fastlane_helper.sh build
```

## 🎯 Available Commands

### Using Helper Script (Recommended)

| Command | Description |
|---------|-------------|
| `./fastlane_helper.sh setup` | Install dependencies |
| `./fastlane_helper.sh build` | Build iOS app |
| `./fastlane_helper.sh test` | Run tests |
| `./fastlane_helper.sh beta` | Deploy to TestFlight |
| `./fastlane_helper.sh release` | Deploy to App Store |
| `./fastlane_helper.sh certs` | Manage certificates |
| `./fastlane_helper.sh sync` | Sync certificates |
| `./fastlane_helper.sh screenshots` | Generate screenshots |
| `./fastlane_helper.sh lanes` | Show all lanes |

### Using Fastlane Directly

```bash
bundle exec fastlane [lane_name]
```

## ⚙️ Configuration

### Environment Variables

Create `fastlane/.env` from the template:

```bash
cp fastlane/.env.default fastlane/.env
```

**Required Variables:**
```env
APPLE_ID=<EMAIL>
ITC_TEAM_ID=your-itc-team-id
MATCH_GIT_URL=https://github.com/your-org/certificates.git
MATCH_PASSWORD=your-match-password
```

**Optional Variables:**
```env
APP_STORE_CONNECT_API_KEY_ID=your-api-key-id
APP_STORE_CONNECT_API_ISSUER_ID=your-issuer-id
SLACK_URL=https://hooks.slack.com/services/your/webhook
```

### App Configuration

The app is configured with:
- **Bundle ID:** `com.travelgator.development`
- **Team ID:** `5K25T9R4G7`
- **Display Name:** TravelGator Staging
- **Deployment Target:** iOS 12.0+

## 📱 Lanes Documentation

### 🔨 Build Lane

```bash
./fastlane_helper.sh build
```

**What it does:**
- Cleans previous Flutter builds
- Gets Flutter dependencies
- Builds Flutter iOS app (release mode)
- Creates signed IPA file
- Outputs to `./build/TravelGator.ipa`

### 🧪 Test Lane

```bash
./fastlane_helper.sh test
```

**What it does:**
- Runs Flutter unit tests
- Runs iOS unit tests in simulator
- Generates test reports

### 🚀 Beta Lane

```bash
./fastlane_helper.sh beta
```

**What it does:**
- Increments build number automatically
- Builds the app
- Uploads to TestFlight
- Skips external tester distribution
- Sends Slack notification (if configured)

### 🏪 Release Lane

```bash
./fastlane_helper.sh release
```

**What it does:**
- Increments version number
- Builds the app
- Uploads to App Store Connect
- Does not auto-submit for review
- Sends Slack notification (if configured)

### 📸 Screenshots Lane

```bash
./fastlane_helper.sh screenshots
```

**What it does:**
- Captures App Store screenshots
- Supports multiple device sizes
- Saves to `fastlane/screenshots/`

## 🔐 Certificate Management

### Initial Setup

```bash
# Initialize match (first time only)
bundle exec fastlane match init

# Create and store certificates
./fastlane_helper.sh sync
```

### Certificate Types

- **Development:** For local testing and debugging
- **App Store:** For App Store distribution

### Match Configuration

Certificates are stored in a private Git repository and encrypted with a password. This ensures team members can share certificates securely.

## 🔄 CI/CD Integration

### GitHub Actions

```yaml
name: iOS Deploy

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
          bundler-cache: true
          working-directory: ios

      - name: Deploy to TestFlight
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          MATCH_GIT_URL: ${{ secrets.MATCH_GIT_URL }}
        run: |
          cd ios
          bundle exec fastlane beta
```

### Environment Variables for CI

Set these secrets in your CI system:
- `APPLE_ID`
- `MATCH_PASSWORD`
- `MATCH_GIT_URL`
- `ITC_TEAM_ID`
- `SLACK_URL` (optional)

## 🐛 Troubleshooting

### Common Issues

#### Certificate Problems
```bash
# Reset certificates
./fastlane_helper.sh sync
```

#### Build Failures
```bash
# Clean everything
flutter clean
cd ios
pod install
./fastlane_helper.sh build
```

#### Upload Issues
- Verify Apple ID credentials
- Check team ID configuration
- Ensure app exists in App Store Connect

### Debug Mode

Add `--verbose` to any Fastlane command:
```bash
bundle exec fastlane build --verbose
```

### Log Files

Check these locations for logs:
- `fastlane/report.xml`
- `fastlane/test_output/`
- Xcode build logs

## 📁 File Structure

```
ios/
├── fastlane/
│   ├── Appfile              # App configuration
│   ├── Fastfile             # Lane definitions
│   ├── Matchfile            # Certificate config
│   ├── .env.default         # Environment template
│   ├── .env                 # Your environment (create this)
│   ├── .gitignore           # Ignore sensitive files
│   └── README.md            # Detailed documentation
├── fastlane_helper.sh       # Easy command wrapper
├── validate_setup.sh        # Setup validation
├── Gemfile                  # Ruby dependencies
└── README.md               # This file
```

## 🤝 Contributing

1. Test changes locally first
2. Update documentation if needed
3. Follow existing code style
4. Test with validation script

## 📚 Resources

- [Fastlane Documentation](https://docs.fastlane.tools/)
- [Match Documentation](https://docs.fastlane.tools/actions/match/)
- [App Store Connect API](https://developer.apple.com/app-store-connect/api/)
- [Flutter iOS Deployment](https://docs.flutter.dev/deployment/ios)

## 📞 Support

For issues with this Fastlane setup:
1. Run `./validate_setup.sh` to check configuration
2. Check the troubleshooting section above
3. Review Fastlane logs
4. Consult the official Fastlane documentation

## 🔧 Advanced Configuration

### Custom Build Configurations

You can modify build settings in `Fastfile`:

```ruby
# Custom build configuration
build_app(
  workspace: "Runner.xcworkspace",
  scheme: "Runner",
  configuration: "Release",
  export_method: "app-store",
  export_options: {
    provisioningProfiles: {
      "com.travelgator.development" => "match AppStore com.travelgator.development"
    }
  }
)
```

### Slack Notifications

Configure Slack webhooks for build notifications:

```env
SLACK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### App Store Connect API

For better authentication, use API keys instead of username/password:

```env
APP_STORE_CONNECT_API_KEY_ID=your-key-id
APP_STORE_CONNECT_API_ISSUER_ID=your-issuer-id
APP_STORE_CONNECT_API_KEY_FILEPATH=./AuthKey_your-key-id.p8
```

### Version Management

Fastlane automatically manages versions:
- `beta` lane increments build number
- `release` lane increments version number
- Manual control available via parameters

## 📊 Monitoring & Analytics

### Build Reports

Fastlane generates reports in:
- `fastlane/report.xml` - Build summary
- `fastlane/test_output/` - Test results
- Build logs for debugging

### TestFlight Analytics

Monitor your beta releases:
- Crash reports
- Usage analytics
- Tester feedback

## 🔄 Maintenance

### Regular Tasks

```bash
# Update Fastlane
bundle update fastlane

# Update certificates (monthly)
./fastlane_helper.sh sync

# Clean old builds
rm -rf fastlane/builds/*
```

### Security Audits

- Rotate match passwords quarterly
- Review team access regularly
- Update API keys as needed

---

<div align="center">
  <strong>Happy Deploying! 🚀</strong>

  Made with ❤️ for TravelGator
</div>
