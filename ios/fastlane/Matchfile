# Git repository for storing certificates and provisioning profiles
git_url(ENV["MATCH_GIT_URL"])

# Storage mode (git, google_cloud, s3, etc.)
storage_mode("git")

# App identifier
app_identifier(["com.travelgator.development"])

# Username for Apple Developer Portal
username(ENV["APPLE_ID"])

# Team ID for Apple Developer Portal
team_id("5K25T9R4G7")

# For more information about match, see:
# https://docs.fastlane.tools/actions/match/
