# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  # Environment setup
  before_all do
    setup_circle_ci if ENV['CI']
  end

  desc "Build the iOS app"
  lane :build do
    # Ensure we're in the right directory
    Dir.chdir("..") do
      # Clean previous builds
      sh("flutter clean")
      
      # Get Flutter dependencies
      sh("flutter pub get")
      
      # Build iOS app
      sh("flutter build ios --release --no-codesign")
    end
    
    # Build with Xcode
    build_app(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      configuration: "Release",
      export_method: "app-store",
      output_directory: "./build",
      output_name: "TravelGator.ipa",
      clean: true,
      include_bitcode: false
    )
  end

  desc "Run tests"
  lane :test do
    # Run Flutter tests
    Dir.chdir("..") do
      sh("flutter test")
    end
    
    # Run iOS unit tests
    run_tests(
      workspace: "Runner.xcworkspace",
      scheme: "Runner",
      device: "iPhone 15"
    )
  end

  desc "Deploy to TestFlight"
  lane :beta do
    # Increment build number
    increment_build_number(xcodeproj: "Runner.xcodeproj")
    
    # Build the app
    build
    
    # Upload to TestFlight
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false,
      notify_external_testers: false
    )
    
    # Send notification
    slack(
      message: "Successfully uploaded TravelGator to TestFlight! 🚀",
      success: true
    ) if ENV["SLACK_URL"]
  end

  desc "Deploy to App Store"
  lane :release do
    # Increment version number
    increment_version_number(xcodeproj: "Runner.xcodeproj")
    
    # Build the app
    build
    
    # Upload to App Store
    upload_to_app_store(
      force: true,
      reject_if_possible: true,
      skip_metadata: false,
      skip_screenshots: false,
      submit_for_review: false
    )
    
    # Send notification
    slack(
      message: "Successfully uploaded TravelGator to App Store! 🎉",
      success: true
    ) if ENV["SLACK_URL"]
  end

  desc "Manage certificates and provisioning profiles"
  lane :certificates do
    match(
      type: "development",
      readonly: true
    )
    
    match(
      type: "appstore",
      readonly: true
    )
  end

  desc "Generate screenshots"
  lane :screenshots do
    capture_screenshots(
      workspace: "Runner.xcworkspace",
      scheme: "Runner"
    )
  end

  desc "Sync certificates and provisioning profiles"
  lane :sync_certificates do
    match(type: "development")
    match(type: "appstore")
  end

  # Error handling
  error do |lane, exception|
    slack(
      message: "Error in lane #{lane}: #{exception.message}",
      success: false
    ) if ENV["SLACK_URL"]
  end
end
