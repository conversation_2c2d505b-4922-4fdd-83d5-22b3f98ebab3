# Fastlane Configuration for TravelGator iOS

This directory contains the Fastlane configuration for automating iOS builds, testing, and deployment for the TravelGator Flutter app.

## Setup

### 1. Install Dependencies

```bash
cd ios
bundle install
```

### 2. Environment Configuration

Copy the environment template and configure your values:

```bash
cp fastlane/.env.default fastlane/.env
```

Edit `fastlane/.env` with your actual values:
- `APPLE_ID`: Your Apple Developer account email
- `ITC_TEAM_ID`: Your iTunes Connect team ID (if different from developer team)
- `MATCH_GIT_URL`: Git repository URL for storing certificates
- `MATCH_PASSWORD`: Password for encrypting certificates
- Other optional configurations

### 3. Certificate Management

Set up match for certificate management:

```bash
# Initialize match (first time only)
bundle exec fastlane match init

# Generate and store certificates
bundle exec fastlane sync_certificates
```

## Available Lanes

### Build Lane
Builds the iOS app for release:

```bash
bundle exec fastlane build
```

This lane:
- Cleans previous Flutter builds
- Gets Flutter dependencies
- Builds Flutter iOS app
- Creates signed IPA file

### Test Lane
Runs tests for the app:

```bash
bundle exec fastlane test
```

This lane:
- Runs Flutter unit tests
- Runs iOS unit tests in simulator

### Beta Lane
Deploys to TestFlight for beta testing:

```bash
bundle exec fastlane beta
```

This lane:
- Increments build number
- Builds the app
- Uploads to TestFlight
- Sends Slack notification (if configured)

### Release Lane
Deploys to App Store:

```bash
bundle exec fastlane release
```

This lane:
- Increments version number
- Builds the app
- Uploads to App Store Connect
- Sends Slack notification (if configured)

### Certificate Management
Manages certificates and provisioning profiles:

```bash
# Download existing certificates
bundle exec fastlane certificates

# Sync/update certificates
bundle exec fastlane sync_certificates
```

### Screenshots
Generates App Store screenshots:

```bash
bundle exec fastlane screenshots
```

## Configuration Files

- `Appfile`: Contains app-specific configuration (bundle ID, team ID, etc.)
- `Fastfile`: Contains all lane definitions and automation logic
- `Matchfile`: Configuration for certificate management
- `.env`: Environment variables (create from `.env.default`)

## Tips

1. **First Time Setup**: Run `bundle exec fastlane sync_certificates` to set up certificates
2. **CI/CD**: Set environment variables in your CI system instead of using `.env` file
3. **API Keys**: Consider using App Store Connect API keys for better authentication
4. **Notifications**: Configure Slack webhook for build notifications
5. **Version Management**: The lanes automatically handle version/build number increments

## Troubleshooting

- **Certificate Issues**: Run `bundle exec fastlane sync_certificates` to refresh
- **Build Failures**: Check Xcode project settings and ensure Flutter build works locally
- **Upload Issues**: Verify App Store Connect credentials and team IDs

For more information, visit [Fastlane Documentation](https://docs.fastlane.tools/)
