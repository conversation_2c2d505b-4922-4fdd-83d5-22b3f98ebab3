# 🚀 Fastlane Quick Reference

## Essential Commands

### Setup & Validation
```bash
./fastlane_helper.sh setup     # Install dependencies
./validate_setup.sh            # Check configuration
```

### Daily Operations
```bash
./fastlane_helper.sh build     # Build app
./fastlane_helper.sh test      # Run tests
./fastlane_helper.sh beta      # Deploy to TestFlight
./fastlane_helper.sh release   # Deploy to App Store
```

### Certificate Management
```bash
./fastlane_helper.sh sync      # Sync certificates
./fastlane_helper.sh certs     # Download certificates
```

### Utilities
```bash
./fastlane_helper.sh lanes     # Show all lanes
./fastlane_helper.sh screenshots  # Generate screenshots
```

## Environment Setup

### First Time Setup
```bash
cd ios
cp fastlane/.env.default fastlane/.env
# Edit .env with your credentials
bundle exec fastlane match init
./fastlane_helper.sh sync
```

### Required Environment Variables
```env
APPLE_ID=<EMAIL>
MATCH_GIT_URL=https://github.com/your-org/certificates.git
MATCH_PASSWORD=your-secure-password
ITC_TEAM_ID=your-itc-team-id
```

## Troubleshooting

### Common Fixes
```bash
# Certificate issues
./fastlane_helper.sh sync

# Build issues
flutter clean && cd ios && ./fastlane_helper.sh build

# Dependency issues
./fastlane_helper.sh setup

# Validation
./validate_setup.sh
```

### Debug Mode
```bash
bundle exec fastlane [lane] --verbose
```

## File Locations

- **Configuration**: `fastlane/Appfile`, `fastlane/Fastfile`
- **Environment**: `fastlane/.env`
- **Certificates**: `fastlane/Matchfile`
- **Logs**: `fastlane/report.xml`, `fastlane/test_output/`
- **Builds**: `fastlane/builds/`

## Lane Details

| Lane | Purpose | Auto-increment |
|------|---------|----------------|
| `build` | Create IPA | No |
| `test` | Run tests | No |
| `beta` | TestFlight | Build number |
| `release` | App Store | Version number |
| `certificates` | Download certs | No |
| `sync_certificates` | Update certs | No |
| `screenshots` | Generate images | No |

## App Configuration

- **Bundle ID**: `com.travelgator.development`
- **Team ID**: `5K25T9R4G7`
- **Display Name**: TravelGator Staging
- **Min iOS**: 12.0

## Support

1. Check `./validate_setup.sh`
2. Review logs in `fastlane/`
3. See full README: `ios/README.md`
4. Fastlane docs: https://docs.fastlane.tools/
