# Apple Sign-In Setup Guide

Apple Sign-In with Firebase requires several configuration steps. Follow this guide to set it up properly.

## Prerequisites

- iOS 13.0 or later
- Apple Developer Account (paid)
- Firebase project with Authentication enabled

## 1. Apple Developer Console Setup

### Step 1: Configure App ID
1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Select **Identifiers** → **App IDs**
4. Find your app ID (should match bundle ID: `com.travelgator.flutterApp`)
5. Edit the App ID and enable **Sign In with Apple** capability
6. Save the changes

### Step 2: Create Service ID (for Firebase)
1. In **Identifiers**, click **+** to create new identifier
2. Select **Services IDs** and continue
3. Create a Service ID with identifier like: `com.travelgator.flutterApp.service`
4. Enable **Sign In with Apple**
5. Configure domains and redirect URLs:
   - **Domains**: `travelgator.firebaseapp.com`
   - **Redirect URLs**: `https://travelgator.firebaseapp.com/__/auth/handler`

### Step 3: Create Private Key
1. Go to **Keys** section
2. Click **+** to create new key
3. Enter key name (e.g., "Apple Sign In Key")
4. Enable **Sign In with Apple**
5. Configure the key for your App ID
6. Download the `.p8` key file (save it securely)
7. Note the **Key ID** (10-character string)

## 2. Firebase Console Setup

### Step 1: Enable Apple Sign-In
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **travelgator**
3. Go to **Authentication** → **Sign-in method**
4. Enable **Apple** provider

### Step 2: Configure Apple Provider
1. **Service ID**: Enter the Service ID created above (`com.travelgator.flutterApp.service`)
2. **Apple team ID**: Find in Apple Developer Console → Membership
3. **Key ID**: The 10-character Key ID from the private key
4. **Private key**: Upload the `.p8` file downloaded earlier
5. Save the configuration

## 3. Xcode Project Configuration

### Step 1: Add Capability
1. Open `ios/Runner.xcworkspace` in Xcode
2. Select **Runner** project
3. Select **Runner** target
4. Go to **Signing & Capabilities** tab
5. Click **+ Capability**
6. Add **Sign In with Apple**

### Step 2: Verify Bundle ID
1. Ensure Bundle Identifier matches: `com.travelgator.flutterApp`
2. This should match the App ID configured in Apple Developer Console

### Step 3: Verify Entitlements
The entitlements file has been created at `ios/Runner/Runner.entitlements` with:
```xml
<key>com.apple.developer.applesignin</key>
<array>
    <string>Default</string>
</array>
```

## 4. Current Implementation Status

✅ **Flutter Dependencies**: Added `sign_in_with_apple: ^6.1.3`
✅ **Entitlements File**: Created `ios/Runner/Runner.entitlements`
✅ **Firebase Integration**: Implemented in auth data sources
✅ **UI Integration**: Added to authentication modal

## 5. Testing

### Prerequisites for Testing
- Physical iOS device (iOS 13+)
- Device signed in to iCloud with Apple ID
- App built with proper provisioning profile

### Test Steps
1. Run app on physical device
2. Trigger authentication modal
3. Tap "Continue with Apple"
4. Should show Apple Sign-In dialog

## 6. Common Issues

### Error 1000 (AuthorizationErrorCode.unknown)
- **Cause**: Missing Apple Developer configuration
- **Solution**: Complete steps 1-3 above

### "Apple Sign-In not available"
- **Cause**: iOS version < 13 or simulator issues
- **Solution**: Test on physical device with iOS 13+

### Firebase Authentication Errors
- **Cause**: Incorrect Firebase configuration
- **Solution**: Verify Service ID and private key setup

## 7. Next Steps

To complete the setup:

1. **Apple Developer Account**: Configure App ID and Service ID
2. **Firebase Console**: Add Apple provider with Service ID and private key
3. **Xcode**: Add Sign In with Apple capability
4. **Test**: Build and test on physical device

Once these steps are completed, Apple Sign-In should work properly with Firebase Authentication.
