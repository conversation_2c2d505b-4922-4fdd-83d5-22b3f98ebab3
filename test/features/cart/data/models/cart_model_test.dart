import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/features/cart/data/models/cart_model.dart';

void main() {
  group('CartItemModel', () {
    test('should create CartItemModel from JSON', () {
      final json = {
        'variantId': 'variant123',
        'productId': 'product456',
        'quantity': 2,
        'price': 29.99,
        'title': 'Test Product',
      };

      final cartItem = CartItemModel.fromJson(json);

      expect(cartItem.variantId, 'variant123');
      expect(cartItem.productId, 'product456');
      expect(cartItem.quantity, 2);
      expect(cartItem.price, 29.99);
      expect(cartItem.title, 'Test Product');
    });

    test('should convert CartItemModel to JSON', () {
      const cartItem = CartItemModel(
        variantId: 'variant123',
        productId: 'product456',
        quantity: 2,
        price: 29.99,
        title: 'Test Product',
      );

      final json = cartItem.toJson();

      expect(json['variantId'], 'variant123');
      expect(json['productId'], 'product456');
      expect(json['quantity'], 2);
      expect(json['price'], 29.99);
      expect(json['title'], 'Test Product');
    });

    test('should create CartItemModel with copyWith', () {
      const cartItem = CartItemModel(
        variantId: 'variant123',
        productId: 'product456',
        quantity: 2,
        price: 29.99,
        title: 'Test Product',
      );

      final updatedItem = cartItem.copyWith(quantity: 3, price: 39.99);

      expect(updatedItem.variantId, 'variant123');
      expect(updatedItem.productId, 'product456');
      expect(updatedItem.quantity, 3);
      expect(updatedItem.price, 39.99);
      expect(updatedItem.title, 'Test Product');
    });
  });

  group('CartModel', () {
    test('should create CartModel from JSON', () {
      final json = {
        'items': [
          {
            'variantId': 'variant123',
            'productId': 'product456',
            'quantity': 2,
            'price': 29.99,
            'title': 'Test Product',
          }
        ],
        'subtotal': 59.98,
        'tax': 5.40,
        'total': 65.38,
      };

      final cart = CartModel.fromJson(json);

      expect(cart.items.length, 1);
      expect(cart.items.first.variantId, 'variant123');
      expect(cart.subtotal, 59.98);
      expect(cart.tax, 5.40);
      expect(cart.total, 65.38);
    });

    test('should convert CartModel to JSON', () {
      const cart = CartModel(
        items: [
          CartItemModel(
            variantId: 'variant123',
            productId: 'product456',
            quantity: 2,
            price: 29.99,
            title: 'Test Product',
          )
        ],
        subtotal: 59.98,
        tax: 5.40,
        total: 65.38,
      );

      final json = cart.toJson();

      expect(json['items'], isA<List>());
      expect((json['items'] as List).length, 1);
      expect(json['subtotal'], 59.98);
      expect(json['tax'], 5.40);
      expect(json['total'], 65.38);
    });

    test('should create CartModel with copyWith', () {
      const cart = CartModel(
        items: [
          CartItemModel(
            variantId: 'variant123',
            productId: 'product456',
            quantity: 2,
            price: 29.99,
            title: 'Test Product',
          )
        ],
        subtotal: 59.98,
        tax: 5.40,
        total: 65.38,
      );

      final updatedCart = cart.copyWith(subtotal: 69.98, total: 75.38);

      expect(updatedCart.items.length, 1);
      expect(updatedCart.subtotal, 69.98);
      expect(updatedCart.tax, 5.40);
      expect(updatedCart.total, 75.38);
    });
  });
}
