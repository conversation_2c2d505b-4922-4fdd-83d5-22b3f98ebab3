import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_travelgator/features/purchase/presentation/screens/pre_purchase_screen.dart';
import 'package:flutter_travelgator/core/models/product_model.dart';

void main() {
  group('PrePurchaseScreen Tests', () {
    late Product testProduct;

    setUp(() {
      // Create a test product
      testProduct = Product(
        id: 'test-product-1',
        title: 'Malaysia eSIM Roaming Data 1- 12GB, 1- 15 days',
        handle: 'malaysia-esim',
        variants: VariantsConnection(
          edges: [
            VariantEdge(
              node: Variant(
                id: 'test-variant-id',
                price: MoneyV2(amount: '1.00', currencyCode: 'USD'),
                compareAtPrice: MoneyV2(amount: '2.00', currencyCode: 'USD'),
              ),
            ),
          ],
        ),
      );
    });

    testWidgets('should display product title', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PrePurchaseScreen(product: testProduct),
          ),
        ),
      );

      expect(find.text('Malaysia eSIM Roaming Data 1- 12GB, 1- 15 days'), findsOneWidget);
    });

    testWidgets('should display header with title', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PrePurchaseScreen(product: testProduct),
          ),
        ),
      );

      expect(find.text('Review your purchase'), findsOneWidget);
    });

    testWidgets('should display data and day dropdowns', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PrePurchaseScreen(product: testProduct),
          ),
        ),
      );

      expect(find.text('Your data'), findsOneWidget);
      expect(find.text('Day'), findsOneWidget);
    });

    testWidgets('should display eSIM type options', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PrePurchaseScreen(product: testProduct),
          ),
        ),
      );

      expect(find.text('New eSIM'), findsOneWidget);
      expect(find.text('Top Up'), findsOneWidget);
    });

    testWidgets('should display action buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PrePurchaseScreen(product: testProduct),
          ),
        ),
      );

      expect(find.text('Add to Card'), findsOneWidget);
      expect(find.text('Buy now'), findsOneWidget);
    });

    // Note: Interactive test for ICCID dropdown would require more complex state management testing
    // The UI correctly shows/hides the ICCID dropdown based on eSIM type selection in manual testing
  });
}
