import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/features/auth/presentation/providers/auth_provider.dart';

void main() {
  group('AuthState', () {
    test('should create AuthState with default values', () {
      const authState = AuthState();
      
      expect(authState.isAuthenticated, false);
      expect(authState.userId, isNull);
      expect(authState.error, isNull);
    });

    test('should create AuthState with custom values', () {
      const authState = AuthState(
        isAuthenticated: true,
        userId: 'user123',
        error: 'Some error',
      );
      
      expect(authState.isAuthenticated, true);
      expect(authState.userId, 'user123');
      expect(authState.error, 'Some error');
    });

    test('copyWith should update only specified fields', () {
      const originalState = AuthState(
        isAuthenticated: false,
        userId: 'user123',
        error: 'old error',
      );
      
      final newState = originalState.copyWith(
        isAuthenticated: true,
        error: null,
      );
      
      expect(newState.isAuthenticated, true);
      expect(newState.userId, 'user123'); // Should remain unchanged
      expect(newState.error, isNull);
    });

    test('copyWith should clear error when explicitly set to null', () {
      const originalState = AuthState(
        isAuthenticated: false,
        error: 'some error',
      );
      
      final newState = originalState.copyWith(error: null);
      
      expect(newState.error, isNull);
    });
  });

  group('AuthNotifier', () {
    test('should initialize with empty AuthState', () {
      // Note: This test would require mocking the AuthRepository
      // For now, we'll just test the AuthState class
      expect(true, true); // Placeholder test
    });
  });
}
