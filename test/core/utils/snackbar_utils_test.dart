import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/core/utils/snackbar_utils.dart';

void main() {
  group('SnackbarUtils', () {
    testWidgets('showSuccess displays success snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showSuccess(context, 'Success message'),
                  child: const Text('Show Success'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Success'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Success message'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle_outline), findsOneWidget);
    });

    testWidgets('showError displays error snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showError(context, 'Error message'),
                  child: const Text('Show Error'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Error'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Error message'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('showAuthSuccess displays auth success snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showAuthSuccess(context),
                  child: const Text('Show Auth Success'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Auth Success'));
      await tester.pump();

      // Verify snackbar is displayed with default message
      expect(find.text('Successfully signed in!'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle_outline), findsOneWidget);
    });

    testWidgets('showAuthError displays auth error snackbar with retry', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showAuthError(
                    context,
                    'Invalid credentials',
                    onRetry: () {}, // Simple callback for testing
                  ),
                  child: const Text('Show Auth Error'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Auth Error'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Sign-in failed: Invalid credentials'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('showLoading displays loading snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showLoading(context, 'Loading...'),
                  child: const Text('Show Loading'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Loading'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Loading...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('showWarning displays warning snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showWarning(context, 'Warning message'),
                  child: const Text('Show Warning'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Warning'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Warning message'), findsOneWidget);
      expect(find.byIcon(Icons.warning_outlined), findsOneWidget);
    });

    testWidgets('showInfo displays info snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showInfo(context, 'Info message'),
                  child: const Text('Show Info'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Info'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Info message'), findsOneWidget);
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets('showCartSuccess displays cart success snackbar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => SnackbarUtils.showCartSuccess(context, 'Item added to cart'),
                  child: const Text('Show Cart Success'),
                );
              },
            ),
          ),
        ),
      );

      // Tap the button to show snackbar
      await tester.tap(find.text('Show Cart Success'));
      await tester.pump();

      // Verify snackbar is displayed
      expect(find.text('Item added to cart'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle_outline), findsOneWidget);
    });
  });
}
