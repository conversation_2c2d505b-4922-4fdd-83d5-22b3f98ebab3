import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/core/utils/price_formatter.dart';
import 'package:flutter_travelgator/core/models/product_model.dart';

void main() {
  group('PriceFormatter', () {
    test('should format price correctly', () {
      final result = PriceFormatter.formatPrice('10.50', 'USD');
      expect(result, equals('USD 10.50'));
    });

    test('should handle invalid price amount', () {
      final result = PriceFormatter.formatPrice('invalid', 'USD');
      expect(result, equals('Price unavailable'));
    });

    test('should create mock product with price data', () {
      // Create a mock product structure for testing
      final mockProduct = Product(
        id: 'test-id',
        title: 'Test Product',
        handle: 'test-handle',
        variants: VariantsConnection(
          edges: [
            VariantEdge(
              node: Variant(
                id: 'test-variant-id-1',
                price: MoneyV2(amount: '25.99', currencyCode: 'USD'),
                compareAtPrice: MoneyV2(amount: '35.99', currencyCode: 'USD'),
              ),
            ),
          ],
        ),
      );

      final priceInfo = PriceFormatter.getProductPriceInfo(mockProduct);
      
      expect(priceInfo.currentPrice, equals('USD 25.99'));
      expect(priceInfo.originalPrice, equals('USD 35.99'));
      expect(priceInfo.hasDiscount, isTrue);
    });

    test('should handle product without compareAtPrice', () {
      final mockProduct = Product(
        id: 'test-id',
        title: 'Test Product',
        handle: 'test-handle',
        variants: VariantsConnection(
          edges: [
            VariantEdge(
              node: Variant(
                id: 'test-variant-id-2',
                price: MoneyV2(amount: '25.99', currencyCode: 'USD'),
                compareAtPrice: null,
              ),
            ),
          ],
        ),
      );

      final priceInfo = PriceFormatter.getProductPriceInfo(mockProduct);
      
      expect(priceInfo.currentPrice, equals('USD 25.99'));
      expect(priceInfo.originalPrice, equals('USD 31.19')); // 25.99 * 1.2
      expect(priceInfo.hasDiscount, isFalse);
    });

    test('should handle product without variants', () {
      final mockProduct = Product(
        id: 'test-id',
        title: 'Test Product',
        handle: 'test-handle',
        variants: VariantsConnection(edges: []),
      );

      final priceInfo = PriceFormatter.getProductPriceInfo(mockProduct);
      
      expect(priceInfo.currentPrice, equals('Price unavailable'));
      expect(priceInfo.originalPrice, equals(''));
      expect(priceInfo.hasDiscount, isFalse);
    });

    test('should get numeric price value', () {
      final mockProduct = Product(
        id: 'test-id',
        title: 'Test Product',
        handle: 'test-handle',
        variants: VariantsConnection(
          edges: [
            VariantEdge(
              node: Variant(
                id: 'test-variant-id-3',
                price: MoneyV2(amount: '25.99', currencyCode: 'USD'),
              ),
            ),
          ],
        ),
      );

      final priceValue = PriceFormatter.getProductPriceValue(mockProduct);
      expect(priceValue, equals(25.99));
    });
  });
}
