import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travelgator/core/errors/failures.dart';

void main() {
  group('Failure Classes', () {
    test('ServerFailure should be instantiable with required message', () {
      const failure = ServerFailure(message: 'Server error occurred');
      
      expect(failure.message, equals('Server error occurred'));
      expect(failure.code, isNull);
      expect(failure.details, isNull);
      expect(failure, isA<Failure>());
    });

    test('AuthFailure should be instantiable with message, code, and details', () {
      const failure = AuthFailure(
        message: 'Authentication failed',
        code: 'AUTH_001',
        details: {'reason': 'invalid_token'},
      );
      
      expect(failure.message, equals('Authentication failed'));
      expect(failure.code, equals('AUTH_001'));
      expect(failure.details, equals({'reason': 'invalid_token'}));
      expect(failure, isA<Failure>());
    });

    test('NetworkFailure should be instantiable', () {
      const failure = NetworkFailure(message: 'Network connection failed');
      
      expect(failure.message, equals('Network connection failed'));
      expect(failure, isA<Failure>());
    });

    test('CacheFailure should have default message', () {
      const failure = CacheFailure();
      
      expect(failure.message, equals('Cache operation failed'));
      expect(failure, isA<Failure>());
    });

    test('CacheFailure should allow custom message', () {
      const failure = CacheFailure(message: 'Custom cache error');
      
      expect(failure.message, equals('Custom cache error'));
      expect(failure, isA<Failure>());
    });

    test('ValidationFailure should handle errors map', () {
      const failure = ValidationFailure(
        message: 'Validation failed',
        errors: {
          'email': ['Email is required', 'Email format is invalid'],
          'password': ['Password is too short'],
        },
      );
      
      expect(failure.message, equals('Validation failed'));
      expect(failure.errors, isNotNull);
      expect(failure.errors!['email'], contains('Email is required'));
      expect(failure, isA<Failure>());
    });

    test('UnknownFailure should have default message', () {
      const failure = UnknownFailure();
      
      expect(failure.message, equals('Unknown error occurred'));
      expect(failure, isA<Failure>());
    });

    test('Failure classes should implement equality correctly', () {
      const failure1 = ServerFailure(message: 'Server error');
      const failure2 = ServerFailure(message: 'Server error');
      const failure3 = ServerFailure(message: 'Different error');
      
      expect(failure1, equals(failure2));
      expect(failure1, isNot(equals(failure3)));
    });

    test('Failure toString should include all properties', () {
      const failure = AuthFailure(
        message: 'Auth error',
        code: 'AUTH_001',
        details: {'key': 'value'},
      );
      
      final string = failure.toString();
      expect(string, contains('Auth error'));
      expect(string, contains('AUTH_001'));
      expect(string, contains('key'));
    });
  });
}
